2025-08-21 10:34:14.764 - 开始执行检查: 服务器=************, 检查项=/etc/passwd文件
2025-08-21 10:34:14.922 - 检查完成: 服务器=************, 检查项=/etc/passwd文件, 结果=成功
2025-08-21 10:50:43.836 - 开始执行检查: 服务器=************, 检查项=/etc/passwd文件
2025-08-21 10:50:43.976 - 检查完成: 服务器=************, 检查项=/etc/passwd文件, 结果=成功
2025-08-21 13:35:31.998 - 开始执行检查: 服务器=************, 检查项=/etc/passwd文件
2025-08-21 13:35:32.183 - 检查完成: 服务器=************, 检查项=/etc/passwd文件, 结果=成功
2025-08-21 13:36:47.078 - 开始执行检查: 服务器=************, 检查项=/etc/hosts文件
2025-08-21 13:36:47.252 - 检查完成: 服务器=************, 检查项=/etc/hosts文件, 结果=成功
2025-08-21 13:36:59.212 - 开始执行检查: 服务器=************, 检查项=/etc/hosts文件
2025-08-21 13:36:59.363 - 检查完成: 服务器=************, 检查项=/etc/hosts文件, 结果=成功
2025-08-21 13:36:59.891 - 开始执行检查: 服务器=************, 检查项=登录失败记录
2025-08-21 13:37:00.127 - 检查完成: 服务器=************, 检查项=登录失败记录, 结果=成功
2025-08-21 13:37:32.030 - 开始执行检查: 服务器=************, 检查项=/etc/hosts文件
2025-08-21 13:37:32.188 - 检查完成: 服务器=************, 检查项=/etc/hosts文件, 结果=成功
2025-08-21 13:37:32.710 - 开始执行检查: 服务器=************, 检查项=登录失败记录
2025-08-21 13:37:32.878 - 检查完成: 服务器=************, 检查项=登录失败记录, 结果=成功
2025-08-21 13:37:33.393 - 开始执行检查: 服务器=************, 检查项=路由表
2025-08-21 13:37:33.548 - 检查完成: 服务器=************, 检查项=路由表, 结果=成功
2025-08-21 13:37:34.057 - 开始执行检查: 服务器=************, 检查项=网络连接
2025-08-21 13:37:34.291 - 检查完成: 服务器=************, 检查项=网络连接, 结果=成功
2025-08-21 13:43:37.510 - 开始执行检查: 服务器=************, 检查项=/etc/passwd文件
2025-08-21 13:43:37.687 - 检查完成: 服务器=************, 检查项=/etc/passwd文件, 结果=成功
2025-08-21 14:34:07.965 - 开始执行检查: 服务器=************, 检查项=/etc/passwd文件
2025-08-21 14:34:08.216 - 检查完成: 服务器=************, 检查项=/etc/passwd文件, 结果=成功
