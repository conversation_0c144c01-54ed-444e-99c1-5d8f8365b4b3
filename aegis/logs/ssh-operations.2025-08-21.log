2025-08-21 10:33:48.508 - SSH连接测试成功: 服务器=************, 地址=************:22
2025-08-21 10:34:14.920 - 服务器: ************, 命令: test -f /etc/passwd && echo 'EXISTS' || echo 'NOT_EXISTS', 退出码: 0, 输出长度: 7
2025-08-21 10:50:40.032 - SSH连接测试成功: 服务器=************, 地址=************:22
2025-08-21 10:50:43.975 - 服务器: ************, 命令: test -f /etc/passwd && echo 'EXISTS' || echo 'NOT_EXISTS', 退出码: 0, 输出长度: 7
2025-08-21 13:35:23.055 - SSH连接测试成功: 服务器=************, 地址=************:22
2025-08-21 13:35:32.183 - 服务器: ************, 命令: test -f /etc/passwd && echo 'EXISTS' || echo 'NOT_EXISTS', 退出码: 0, 输出长度: 7
2025-08-21 13:36:47.252 - 服务器: ************, 命令: test -f /etc/hosts && echo 'EXISTS' || echo 'NOT_EXISTS', 退出码: 0, 输出长度: 7
2025-08-21 13:36:59.363 - 服务器: ************, 命令: test -f /etc/hosts && echo 'EXISTS' || echo 'NOT_EXISTS', 退出码: 0, 输出长度: 7
2025-08-21 13:37:00.125 - 服务器: ************, 命令: lastb | head -10 2>/dev/null || echo '无登录失败记录', 退出码: 0, 输出长度: 115
2025-08-21 13:37:32.188 - 服务器: ************, 命令: test -f /etc/hosts && echo 'EXISTS' || echo 'NOT_EXISTS', 退出码: 0, 输出长度: 7
2025-08-21 13:37:32.878 - 服务器: ************, 命令: lastb | head -10 2>/dev/null || echo '无登录失败记录', 退出码: 0, 输出长度: 115
2025-08-21 13:37:33.545 - 服务器: ************, 命令: ip route show, 退出码: 0, 输出长度: 344
2025-08-21 13:37:34.290 - 服务器: ************, 命令: netstat -tuln, 退出码: 0, 输出长度: 5963
2025-08-21 13:43:31.141 - SSH连接测试成功: 服务器=************, 地址=************:22
2025-08-21 13:43:37.686 - 服务器: ************, 命令: test -f /etc/passwd && echo 'EXISTS' || echo 'NOT_EXISTS', 退出码: 0, 输出长度: 7
2025-08-21 14:00:12.633 - SSH连接测试成功: 服务器=************, 地址=************:22
2025-08-21 14:00:12.822 - 服务器: ************, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3337
2025-08-21 14:09:16.255 - SSH连接测试成功: 服务器=************, 地址=************:22
2025-08-21 14:09:16.413 - 服务器: ************, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3337
2025-08-21 14:16:07.833 - SSH连接测试成功: 服务器=************, 地址=************:22
2025-08-21 14:16:08.007 - 服务器: ************, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3337
2025-08-21 14:18:46.837 - SSH连接测试成功: 服务器=************, 地址=************:22
2025-08-21 14:18:47.018 - 服务器: ************, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3337
2025-08-21 14:26:49.581 - SSH连接测试成功: 服务器=************, 地址=************:22
2025-08-21 14:26:49.837 - 服务器: ************, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3337
2025-08-21 14:34:03.919 - SSH连接测试成功: 服务器=************, 地址=************:22
2025-08-21 14:34:04.208 - 服务器: ************, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3337
2025-08-21 14:34:08.077 - SSH连接测试成功: 服务器=************, 地址=************:22
2025-08-21 14:34:08.216 - 服务器: ************, 命令: test -f /etc/passwd && echo 'EXISTS' || echo 'NOT_EXISTS', 退出码: 0, 输出长度: 7
