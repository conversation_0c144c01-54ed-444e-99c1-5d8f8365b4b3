2025-09-01 14:27:36.701 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-09-01 14:27:36.954 - 服务器: 10.12.135.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3325
2025-09-01 14:27:37.314 - 服务器: 10.12.135.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3325
2025-09-01 15:10:47.430 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-09-01 15:10:47.567 - 服务器: 10.12.135.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3325
2025-09-01 15:10:47.975 - 服务器: 10.12.135.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3325
2025-09-01 15:11:08.742 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-09-01 15:11:08.974 - 服务器: 10.12.135.12, 命令: grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'cet_auth-push', 退出码: 0, 输出长度: 29
2025-09-01 15:11:14.930 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-09-01 15:11:15.050 - 服务器: 10.12.135.12, 命令: grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'sms_swtich', 退出码: 0, 输出长度: 26
2025-09-01 15:11:19.269 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-09-01 15:11:19.408 - 服务器: 10.12.135.12, 命令: ls /etc/CET/only-report/config/ 2>/dev/null || echo 'directory not found', 退出码: 0, 输出长度: 197
2025-09-01 15:11:23.129 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-09-01 15:11:23.252 - 服务器: 10.12.135.12, 命令: grep -n $'\t' /etc/CET/docker/docker-compose.yml 2>/dev/null || echo 'no tabs found', 退出码: 0, 输出长度: 14
2025-09-01 15:11:27.166 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-09-01 15:11:27.293 - 服务器: 10.12.135.12, 命令: grep 'WspCfgShareSize=' /etc/CET/Common/Share.ini | cut -d'=' -f2, 退出码: 0, 输出长度: 8
2025-09-01 15:11:27.371 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-09-01 15:11:27.513 - 服务器: 10.12.135.12, 命令: grep 'WspCfgShareSize=' /etc/CET/Common/Share.ini | cut -d'=' -f2, 退出码: 0, 输出长度: 8
2025-09-01 15:11:33.234 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-09-01 15:11:33.493 - 服务器: 10.12.135.12, 命令: grep '\[Clear\]' /etc/CET/Common/FrontSC.ini 2>/dev/null || echo 'not found', 退出码: 0, 输出长度: 9
2025-09-01 15:12:46.692 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-09-01 15:12:46.836 - 服务器: 10.12.135.12, 命令: grep '\[Clear\]' /etc/CET/Common/FrontSC.ini 2>/dev/null || echo 'not found', 退出码: 0, 输出长度: 9
2025-09-01 15:13:38.078 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-09-01 15:13:42.389 - 服务器: 10.12.135.12, 命令: docker ps --format '{{.Names}}' | head -10, 退出码: 0, 输出长度: 228
2025-09-01 15:13:42.780 - 服务器: 10.12.135.12, 命令: docker logs --tail 500 docker-data-center-service-epms-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10, 退出码: 0, 输出长度: 2156
2025-09-01 15:13:43.031 - 服务器: 10.12.135.12, 命令: docker logs --tail 500 docker-dcim-web-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10, 退出码: 0, 输出长度: 0
2025-09-01 15:13:43.311 - 服务器: 10.12.135.12, 命令: docker logs --tail 500 docker-cloud-auth-service-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10, 退出码: 0, 输出长度: 0
2025-09-01 15:13:43.545 - 服务器: 10.12.135.12, 命令: docker logs --tail 500 flink-dc 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10, 退出码: 0, 输出长度: 1119
2025-09-01 15:13:43.823 - 服务器: 10.12.135.12, 命令: docker logs --tail 500 docker-acmai-service-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10, 退出码: 0, 输出长度: 0
2025-09-01 15:13:44.168 - 服务器: 10.12.135.12, 命令: docker logs --tail 500 common-pechealth-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10, 退出码: 0, 输出长度: 1492
2025-09-01 15:13:44.467 - 服务器: 10.12.135.12, 命令: docker logs --tail 500 common-configserver-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10, 退出码: 0, 输出长度: 890
2025-09-01 15:13:44.746 - 服务器: 10.12.135.12, 命令: docker logs --tail 500 common-datacacheupload-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10, 退出码: 0, 输出长度: 920
2025-09-01 15:13:45.013 - 服务器: 10.12.135.12, 命令: docker logs --tail 500 common-datacachelibserver-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10, 退出码: 0, 输出长度: 0
2025-09-01 15:13:45.244 - 服务器: 10.12.135.12, 命令: docker logs --tail 500 common-intellialarm-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10, 退出码: 0, 输出长度: 890
2025-09-01 15:14:22.926 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-09-01 15:14:23.167 - 服务器: 10.12.135.12, 命令: docker ps --format 'table {{.Image}}' | grep netclient, 退出码: 0, 输出长度: 50
2025-09-01 15:14:23.432 - 服务器: 10.12.135.12, 命令: docker ps --format 'table {{.Image}}' | grep netclient, 退出码: 0, 输出长度: 50
