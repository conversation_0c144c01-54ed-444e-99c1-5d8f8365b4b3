2025-08-25 13:58:21.397 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-25 13:58:21.485 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 22012 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-25 13:58:21.486 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-25 13:58:23.581 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-25 13:58:24.215 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-25 13:58:24.217 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-25 13:58:24.218 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-25 13:58:24.513 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-25 13:58:24.842 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-25 13:58:24.949 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共19个检查项
2025-08-25 13:58:25.389 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-25 13:58:25.431 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 5.164 seconds (JVM running for 8.398)
2025-08-25 14:01:23.294 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-25 14:03:41.433 [http-nio-8080-exec-5] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-25 14:03:41.433 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:03:41.969 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:03:41.970 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-25 14:03:47.103 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 4945
2025-08-25 14:03:47.139 [http-nio-8080-exec-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-25 14:03:47.364 [http-nio-8080-exec-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-25 14:03:47.369 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-25 14:03:52.502 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 4945
2025-08-25 14:04:17.790 [http-nio-8080-exec-6] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:04:17.790 [http-nio-8080-exec-6] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 14:04:23.140 [http-nio-8080-exec-3] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:04:23.140 [http-nio-8080-exec-3] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 14:04:23.141 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:04:23.215 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:04:23.216 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'cet_auth-push'
2025-08-25 14:04:28.395 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 30
2025-08-25 14:27:58.771 [http-nio-8080-exec-8] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:27:58.772 [http-nio-8080-exec-8] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 14:27:58.772 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:27:58.834 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:27:58.834 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep -n $'\t' /etc/CET/docker/docker-compose.yml 2>/dev/null || echo 'no tabs found'
2025-08-25 14:28:03.963 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 14
2025-08-25 14:28:34.033 [http-nio-8080-exec-9] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:28:34.033 [http-nio-8080-exec-9] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 14:28:34.034 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:28:34.118 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:28:34.119 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - date +%s
2025-08-25 14:28:39.260 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 11
2025-08-25 14:28:39.261 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:28:44.351 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:28:44.352 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - date +%s
2025-08-25 14:28:44.491 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 11
2025-08-25 14:28:51.091 [http-nio-8080-exec-10] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:28:51.092 [http-nio-8080-exec-10] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 14:28:51.092 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:28:51.149 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:28:51.150 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}'
2025-08-25 14:28:56.496 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 55
2025-08-25 14:28:56.500 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:29:01.589 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:29:01.589 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}'
2025-08-25 14:29:06.946 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 54
2025-08-25 14:29:06.947 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:29:07.009 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:29:07.010 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}'
2025-08-25 14:29:07.368 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 55
2025-08-25 14:29:07.369 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}'
2025-08-25 14:29:07.711 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 55
2025-08-25 14:29:07.712 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}'
2025-08-25 14:29:13.058 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 55
2025-08-25 14:30:07.973 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:30:07.973 [http-nio-8080-exec-1] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 14:30:07.974 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:30:08.039 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:30:08.039 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format '{{.Names}}' | head -10
2025-08-25 14:30:13.302 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 238
2025-08-25 14:30:13.302 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-dcim-web-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-25 14:30:18.559 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-08-25 14:30:18.559 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 flink-dc 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-25 14:30:23.806 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-08-25 14:30:23.807 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-only-report-docker-service-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-25 14:30:29.040 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 474
2025-08-25 14:30:29.041 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-service-monitor-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-25 14:30:34.834 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 552
2025-08-25 14:30:34.834 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-filemanager-service-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-25 14:30:40.077 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 1018
2025-08-25 14:30:40.077 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-data-center-service-epms-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-25 14:30:45.351 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 1083
2025-08-25 14:30:45.352 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-api-gateway-1-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-25 14:30:50.603 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-08-25 14:30:50.604 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-model-service-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-25 14:30:50.837 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 1692
2025-08-25 14:30:50.837 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 prometheus 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-25 14:30:56.072 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 1105
2025-08-25 14:30:56.072 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-sloth-monitor-server-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-25 14:31:01.343 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-08-25 14:33:47.253 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:33:47.253 [http-nio-8080-exec-4] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 14:33:47.254 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:33:47.385 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:33:47.385 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep netclient
2025-08-25 14:33:52.621 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 50
2025-08-25 14:33:52.622 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:33:52.671 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:33:52.671 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep netclient
2025-08-25 14:33:57.919 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 50
2025-08-25 14:35:56.022 [http-nio-8080-exec-6] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:35:56.022 [http-nio-8080-exec-6] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 14:35:56.023 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:35:56.074 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:35:56.074 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'sms_swtich'
2025-08-25 14:36:01.225 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 27
2025-08-25 14:36:28.215 [http-nio-8080-exec-3] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:36:28.216 [http-nio-8080-exec-3] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 14:36:28.216 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:36:28.296 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:36:28.296 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - ls /etc/CET/only-report/config/ 2>/dev/null || echo 'directory not found'
2025-08-25 14:36:33.443 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 128
2025-08-25 14:38:00.228 [http-nio-8080-exec-8] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:38:00.228 [http-nio-8080-exec-8] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 14:38:00.229 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:38:00.283 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:38:00.283 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - ssh -o BatchMode=yes -o ConnectTimeout=5 ************ 'echo success' 2>/dev/null || echo 'failed'
2025-08-25 14:38:05.530 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 8
2025-08-25 14:38:05.530 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:38:05.603 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:38:05.603 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - ssh -o BatchMode=yes -o ConnectTimeout=5 ************ 'echo success' 2>/dev/null || echo 'failed'
2025-08-25 14:38:10.849 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 8
2025-08-25 14:38:20.148 [http-nio-8080-exec-9] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:38:20.148 [http-nio-8080-exec-9] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 14:38:20.148 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:38:20.233 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:38:20.234 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - ls -1 /var/cache/CET/filemanager/ 2>/dev/null | sort
2025-08-25 14:38:25.401 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 154
2025-08-25 14:38:25.401 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:38:25.454 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:38:25.454 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - ls -1 /var/cache/CET/filemanager/ 2>/dev/null | sort
2025-08-25 14:38:25.589 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 154
2025-08-25 14:38:45.843 [http-nio-8080-exec-10] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:38:45.844 [http-nio-8080-exec-10] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 14:38:45.844 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:38:45.923 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:38:45.924 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort
2025-08-25 14:38:51.168 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 1792
2025-08-25 14:38:51.168 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:38:51.225 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:38:51.226 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort
2025-08-25 14:38:51.447 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 2145
2025-08-25 14:40:05.511 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:40:05.511 [http-nio-8080-exec-2] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 14:40:05.511 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:40:05.570 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:40:05.570 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep device-data-service
2025-08-25 14:40:10.817 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 55
2025-08-25 14:40:10.817 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:40:10.863 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:40:10.864 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep device-data-service
2025-08-25 14:40:16.096 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 55
2025-08-25 14:40:28.767 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:40:28.768 [http-nio-8080-exec-4] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 14:40:28.768 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:40:28.827 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:40:28.827 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive'
2025-08-25 14:40:34.069 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 7
2025-08-25 14:40:34.070 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 14:40:34.131 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 14:40:34.132 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive'
2025-08-25 14:40:34.364 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 7
2025-08-25 14:41:54.675 [http-nio-8080-exec-6] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:41:54.675 [http-nio-8080-exec-6] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 14:41:54.677 [http-nio-8080-exec-6] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-08-25 14:41:54.684 [http-nio-8080-exec-6] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-08-25 14:42:08.222 [http-nio-8080-exec-3] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:42:08.223 [http-nio-8080-exec-3] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 14:43:16.142 [http-nio-8080-exec-8] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 14:43:16.142 [http-nio-8080-exec-8] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 15:40:14.342 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-25 15:40:14.343 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 15:40:14.434 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 15:40:14.434 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-25 15:40:19.562 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 4945
2025-08-25 15:40:19.564 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-25 15:40:24.707 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 4945
2025-08-25 15:40:55.688 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 15:40:55.689 [http-nio-8080-exec-2] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 15:40:59.979 [http-nio-8080-exec-5] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 15:40:59.979 [http-nio-8080-exec-5] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 15:40:59.979 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 15:41:00.039 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 15:41:00.040 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'cet_auth-push'
2025-08-25 15:41:05.189 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 30
2025-08-25 15:41:38.865 [http-nio-8080-exec-6] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 15:41:38.866 [http-nio-8080-exec-6] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 15:41:38.866 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 15:41:38.934 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 15:41:38.935 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'sms_swtich'
2025-08-25 15:41:44.078 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 27
2025-08-25 15:42:46.349 [http-nio-8080-exec-7] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 15:42:46.349 [http-nio-8080-exec-7] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 15:42:46.350 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 15:42:46.428 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 15:42:46.429 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep -n $'\t' /etc/CET/docker/docker-compose.yml 2>/dev/null || echo 'no tabs found'
2025-08-25 15:42:51.568 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 14
2025-08-25 15:43:23.098 [http-nio-8080-exec-8] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 15:43:23.098 [http-nio-8080-exec-8] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 15:43:31.400 [http-nio-8080-exec-9] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 15:43:31.400 [http-nio-8080-exec-9] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 15:43:41.558 [http-nio-8080-exec-10] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 15:43:41.558 [http-nio-8080-exec-10] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 17:39:40.896 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-25 17:39:40.904 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 13912 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-25 17:39:40.905 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-25 17:39:41.797 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-25 17:39:42.289 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-25 17:39:42.292 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-25 17:39:42.292 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-25 17:39:42.451 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-25 17:39:42.651 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-25 17:39:42.671 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共20个检查项
2025-08-25 17:39:42.963 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-25 17:39:42.994 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.681 seconds (JVM running for 3.175)
2025-08-25 17:40:49.860 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-25 17:42:12.605 [http-nio-8080-exec-6] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-25 17:42:12.605 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 17:42:13.105 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 17:42:13.106 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-25 17:42:18.255 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 4945
2025-08-25 17:42:18.282 [http-nio-8080-exec-6] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-25 17:42:18.390 [http-nio-8080-exec-6] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-25 17:42:18.397 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-25 17:42:23.560 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 4945
2025-08-25 17:42:42.529 [http-nio-8080-exec-7] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 17:42:42.529 [http-nio-8080-exec-7] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 17:42:42.531 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 17:42:43.484 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 17:42:43.484 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep 'WspCfgShareSize=' /etc/CET/Common/Share.ini 2>/dev/null || echo 'config not found'
2025-08-25 17:42:43.614 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 24
2025-08-25 17:42:52.932 [http-nio-8080-exec-8] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 17:42:52.932 [http-nio-8080-exec-8] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 17:42:52.932 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 17:42:53.049 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 17:42:53.050 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep 'WspCfgShareSize=' /etc/CET/Common/Share.ini 2>/dev/null || echo 'config not found'
2025-08-25 17:42:53.220 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 24
2025-08-25 17:43:00.650 [http-nio-8080-exec-9] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 17:43:00.650 [http-nio-8080-exec-9] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 17:43:00.651 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 17:43:00.742 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 17:43:00.742 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep 'WspCfgShareSize=' /etc/CET/Common/Share.ini 2>/dev/null || echo 'config not found'
2025-08-25 17:43:00.896 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 24
2025-08-25 17:43:09.040 [http-nio-8080-exec-10] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 17:43:09.041 [http-nio-8080-exec-10] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 17:43:09.041 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 17:43:09.109 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 17:43:09.110 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep 'WspCfgShareSize=' /etc/CET/Common/Share.ini 2>/dev/null || echo 'config not found'
2025-08-25 17:43:09.233 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 24
2025-08-25 17:43:12.022 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 17:43:12.023 [http-nio-8080-exec-1] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 17:43:12.023 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 17:43:12.104 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 17:43:12.105 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep 'WspCfgShareSize=' /etc/CET/Common/Share.ini 2>/dev/null || echo 'config not found'
2025-08-25 17:43:12.232 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 24
2025-08-25 17:43:24.276 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-25 17:43:24.276 [http-nio-8080-exec-2] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-25 17:43:24.277 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-25 17:43:24.375 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-25 17:43:24.375 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep 'WspCfgShareSize=' /etc/CET/Common/Share.ini 2>/dev/null || echo 'config not found'
2025-08-25 17:43:24.529 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 24
