2025-08-25 14:03:41.947 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-25 14:03:47.103 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 4945
2025-08-25 14:03:52.502 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 4945
2025-08-25 14:04:23.215 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-25 14:04:28.395 - 服务器: 10.12.137.14, 命令: grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'cet_auth-push', 退出码: 0, 输出长度: 30
2025-08-25 14:27:58.833 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-25 14:28:03.963 - 服务器: 10.12.137.14, 命令: grep -n $'\t' /etc/CET/docker/docker-compose.yml 2>/dev/null || echo 'no tabs found', 退出码: 0, 输出长度: 14
2025-08-25 14:28:34.117 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-25 14:28:39.259 - 服务器: 10.12.137.13, 命令: date +%s, 退出码: 0, 输出长度: 11
2025-08-25 14:28:44.351 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-25 14:28:44.491 - 服务器: 10.12.137.12, 命令: date +%s, 退出码: 0, 输出长度: 11
2025-08-25 14:28:51.148 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-25 14:28:56.496 - 服务器: 10.12.137.14, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}', 退出码: 0, 输出长度: 55
2025-08-25 14:29:01.589 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-25 14:29:06.946 - 服务器: 10.12.137.13, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}', 退出码: 0, 输出长度: 54
2025-08-25 14:29:07.009 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-25 14:29:07.368 - 服务器: 10.12.137.12, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}', 退出码: 0, 输出长度: 55
2025-08-25 14:29:07.711 - 服务器: 10.12.137.12, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}', 退出码: 0, 输出长度: 55
2025-08-25 14:29:13.058 - 服务器: 10.12.137.13, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}', 退出码: 0, 输出长度: 55
2025-08-25 14:30:08.039 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-25 14:30:13.302 - 服务器: 10.12.137.14, 命令: docker ps --format '{{.Names}}' | head -10, 退出码: 0, 输出长度: 238
2025-08-25 14:30:18.558 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 docker-dcim-web-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 0
2025-08-25 14:30:23.806 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 flink-dc 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 0
2025-08-25 14:30:29.040 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 docker-only-report-docker-service-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 474
2025-08-25 14:30:34.834 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 docker-service-monitor-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 552
2025-08-25 14:30:40.076 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 docker-filemanager-service-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 1018
2025-08-25 14:30:45.351 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 docker-data-center-service-epms-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 1083
2025-08-25 14:30:50.603 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 docker-api-gateway-1-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 0
2025-08-25 14:30:50.837 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 docker-model-service-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 1692
2025-08-25 14:30:56.072 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 prometheus 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 1105
2025-08-25 14:31:01.343 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 docker-sloth-monitor-server-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 0
2025-08-25 14:33:47.384 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-25 14:33:52.621 - 服务器: 10.12.137.12, 命令: docker ps --format 'table {{.Image}}' | grep netclient, 退出码: 0, 输出长度: 50
2025-08-25 14:33:52.670 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-25 14:33:57.918 - 服务器: 10.12.137.13, 命令: docker ps --format 'table {{.Image}}' | grep netclient, 退出码: 0, 输出长度: 50
2025-08-25 14:35:56.073 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-25 14:36:01.225 - 服务器: 10.12.137.14, 命令: grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'sms_swtich', 退出码: 0, 输出长度: 27
2025-08-25 14:36:28.296 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-25 14:36:33.443 - 服务器: 10.12.137.14, 命令: ls /etc/CET/only-report/config/ 2>/dev/null || echo 'directory not found', 退出码: 0, 输出长度: 128
2025-08-25 14:38:00.282 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-25 14:38:05.529 - 服务器: 10.12.137.13, 命令: ssh -o BatchMode=yes -o ConnectTimeout=5 10.12.137.12 'echo success' 2>/dev/null || echo 'failed', 退出码: 0, 输出长度: 8
2025-08-25 14:38:05.603 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-25 14:38:10.849 - 服务器: 10.12.137.12, 命令: ssh -o BatchMode=yes -o ConnectTimeout=5 10.12.137.13 'echo success' 2>/dev/null || echo 'failed', 退出码: 0, 输出长度: 8
2025-08-25 14:38:20.233 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-25 14:38:25.401 - 服务器: 10.12.137.13, 命令: ls -1 /var/cache/CET/filemanager/ 2>/dev/null | sort, 退出码: 0, 输出长度: 154
2025-08-25 14:38:25.454 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-25 14:38:25.589 - 服务器: 10.12.137.12, 命令: ls -1 /var/cache/CET/filemanager/ 2>/dev/null | sort, 退出码: 0, 输出长度: 154
2025-08-25 14:38:45.923 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-25 14:38:51.168 - 服务器: 10.12.137.13, 命令: docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort, 退出码: 0, 输出长度: 1792
2025-08-25 14:38:51.224 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-25 14:38:51.447 - 服务器: 10.12.137.12, 命令: docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort, 退出码: 0, 输出长度: 2145
2025-08-25 14:40:05.569 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-25 14:40:10.816 - 服务器: 10.12.137.13, 命令: docker ps --format 'table {{.Image}}' | grep device-data-service, 退出码: 0, 输出长度: 55
2025-08-25 14:40:10.863 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-25 14:40:16.096 - 服务器: 10.12.137.12, 命令: docker ps --format 'table {{.Image}}' | grep device-data-service, 退出码: 0, 输出长度: 55
2025-08-25 14:40:28.826 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-25 14:40:34.069 - 服务器: 10.12.137.13, 命令: systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive', 退出码: 0, 输出长度: 7
2025-08-25 14:40:34.131 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-25 14:40:34.364 - 服务器: 10.12.137.12, 命令: systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive', 退出码: 0, 输出长度: 7
2025-08-25 15:40:14.433 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-25 15:40:19.562 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 4945
2025-08-25 15:40:24.707 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 4945
2025-08-25 15:41:00.039 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-25 15:41:05.188 - 服务器: 10.12.137.14, 命令: grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'cet_auth-push', 退出码: 0, 输出长度: 30
2025-08-25 15:41:38.933 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-25 15:41:44.078 - 服务器: 10.12.137.14, 命令: grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'sms_swtich', 退出码: 0, 输出长度: 27
2025-08-25 15:42:46.427 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-25 15:42:51.568 - 服务器: 10.12.137.14, 命令: grep -n $'\t' /etc/CET/docker/docker-compose.yml 2>/dev/null || echo 'no tabs found', 退出码: 0, 输出长度: 14
2025-08-25 17:42:13.094 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-25 17:42:18.254 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 4945
2025-08-25 17:42:23.560 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 4945
2025-08-25 17:42:43.482 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-25 17:42:43.613 - 服务器: 10.12.137.12, 命令: grep 'WspCfgShareSize=' /etc/CET/Common/Share.ini 2>/dev/null || echo 'config not found', 退出码: 0, 输出长度: 24
2025-08-25 17:42:53.048 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-25 17:42:53.220 - 服务器: 10.12.137.12, 命令: grep 'WspCfgShareSize=' /etc/CET/Common/Share.ini 2>/dev/null || echo 'config not found', 退出码: 0, 输出长度: 24
2025-08-25 17:43:00.741 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-25 17:43:00.896 - 服务器: 10.12.137.12, 命令: grep 'WspCfgShareSize=' /etc/CET/Common/Share.ini 2>/dev/null || echo 'config not found', 退出码: 0, 输出长度: 24
2025-08-25 17:43:09.109 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-25 17:43:09.233 - 服务器: 10.12.137.12, 命令: grep 'WspCfgShareSize=' /etc/CET/Common/Share.ini 2>/dev/null || echo 'config not found', 退出码: 0, 输出长度: 24
2025-08-25 17:43:12.103 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-25 17:43:12.232 - 服务器: 10.12.137.12, 命令: grep 'WspCfgShareSize=' /etc/CET/Common/Share.ini 2>/dev/null || echo 'config not found', 退出码: 0, 输出长度: 24
2025-08-25 17:43:24.373 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-25 17:43:24.529 - 服务器: 10.12.137.12, 命令: grep 'WspCfgShareSize=' /etc/CET/Common/Share.ini 2>/dev/null || echo 'config not found', 退出码: 0, 输出长度: 24
