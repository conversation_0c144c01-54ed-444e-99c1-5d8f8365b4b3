2025-08-22 08:50:21.902 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 08:50:21.914 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 14888 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-22 08:50:21.927 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-22 08:50:22.696 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-22 08:50:23.208 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 08:50:23.210 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 08:50:23.210 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-22 08:50:23.410 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 08:50:23.603 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-22 08:50:23.622 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共19个检查项
2025-08-22 08:50:23.923 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 08:50:23.951 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.648 seconds (JVM running for 3.172)
2025-08-22 08:50:24.709 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 10:27:37.093 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 10:27:37.113 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 31908 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-22 10:27:37.113 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-22 10:27:37.940 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-22 10:27:38.515 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 10:27:38.520 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 10:27:38.520 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-22 10:27:38.739 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 10:27:38.948 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-22 10:27:38.968 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共19个检查项
2025-08-22 10:27:39.280 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 10:27:39.311 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.942 seconds (JVM running for 3.54)
2025-08-22 10:27:46.715 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 10:27:49.685 [http-nio-8080-exec-3] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 10:27:49.686 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 10:27:50.247 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 10:27:50.248 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 10:27:50.510 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-08-22 10:27:50.525 [http-nio-8080-exec-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 10:27:50.660 [http-nio-8080-exec-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-22 10:27:50.669 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 10:27:50.924 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-08-22 10:31:36.307 [http-nio-8080-exec-5] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 10:31:36.307 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 10:31:36.369 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 10:31:36.369 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 10:31:36.524 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-08-22 10:31:36.526 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 10:31:36.771 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-08-22 10:31:38.738 [http-nio-8080-exec-7] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 10:31:38.738 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 10:31:38.884 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 10:31:38.884 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 10:31:39.178 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-08-22 10:31:39.181 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 10:31:39.367 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-08-22 10:31:45.831 [http-nio-8080-exec-8] INFO  c.c.d.c.ServerCheckController - 测试数据库连接: ************:5432
2025-08-22 10:31:55.177 [http-nio-8080-exec-9] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: null - 检查项数量: 1
2025-08-22 10:31:55.184 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: null
2025-08-22 10:31:55.186 [http-nio-8080-exec-9] ERROR com.cet.datacenter.util.SshUtil - SSH连接测试失败: 服务器=null, 地址=null:null, 错误=null
2025-08-22 10:31:55.186 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: null - 失败
2025-08-22 10:33:34.100 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: null - 检查项数量: 1
2025-08-22 10:33:34.101 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: null
2025-08-22 10:33:34.103 [http-nio-8080-exec-2] ERROR com.cet.datacenter.util.SshUtil - SSH连接测试失败: 服务器=null, 地址=null:null, 错误=null
2025-08-22 10:33:34.103 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: null - 失败
2025-08-22 10:37:54.867 [http-nio-8080-exec-6] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 10:37:54.867 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 10:37:54.960 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 10:37:54.960 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 10:37:55.119 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-08-22 10:37:55.121 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 10:37:55.288 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-08-22 10:39:58.164 [http-nio-8080-exec-8] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 10:39:58.164 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 10:40:00.240 [http-nio-8080-exec-8] ERROR com.cet.datacenter.util.SshUtil - SSH连接测试失败: 服务器=************, 地址=************:22, 错误=Auth fail
2025-08-22 10:40:00.240 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 失败
2025-08-22 10:40:55.758 [http-nio-8080-exec-9] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 10:40:55.759 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 10:40:55.822 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 10:40:55.823 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 10:40:55.988 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 10:40:55.990 [http-nio-8080-exec-9] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-08-22 10:40:56.000 [http-nio-8080-exec-9] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-08-22 10:40:56.000 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 10:40:56.142 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 10:41:04.498 [http-nio-8080-exec-10] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 10:41:04.499 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 10:41:04.559 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 10:41:04.559 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 10:41:04.706 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 10:41:04.709 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 10:41:04.844 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 10:41:06.015 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 10:41:06.016 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 10:41:16.102 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 10:41:16.102 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 10:41:21.247 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 4945
2025-08-22 10:41:21.248 [http-nio-8080-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Starting...
2025-08-22 10:41:21.260 [http-nio-8080-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Start completed.
2025-08-22 10:41:21.260 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 10:41:31.463 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 4945
2025-08-22 10:59:37.685 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 10:59:37.709 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication v1.0-SNAPSHOT using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 35752 (D:\git20250402\aegis\target\aegis-1.0-SNAPSHOT.jar started by Administrator in D:\git20250402\aegis)
2025-08-22 10:59:37.718 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-22 10:59:38.418 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-22 10:59:38.959 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 10:59:38.962 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 10:59:38.963 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-22 10:59:39.094 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 10:59:39.449 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-22 10:59:39.490 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共19个检查项
2025-08-22 10:59:39.859 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 10:59:39.898 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.703 seconds (JVM running for 3.208)
2025-08-22 11:00:41.193 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 11:00:59.481 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 11:00:59.482 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:01:00.040 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:01:00.041 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 11:01:00.167 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 11:01:00.177 [http-nio-8080-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 11:01:00.326 [http-nio-8080-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-22 11:01:00.333 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 11:01:00.502 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 11:01:16.300 [http-nio-8080-exec-5] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 11:01:16.300 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:01:16.387 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:01:16.387 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 11:01:16.535 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 11:01:16.537 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 11:01:16.688 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 11:01:18.387 [http-nio-8080-exec-6] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 11:01:18.388 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:01:38.485 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:01:38.486 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 11:01:58.628 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 4945
2025-08-22 11:01:58.628 [http-nio-8080-exec-6] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-08-22 11:01:58.638 [http-nio-8080-exec-6] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-08-22 11:01:58.638 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 11:01:59.434 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: null - 检查项数量: 1
2025-08-22 11:02:19.143 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 11:02:19.155 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 33424 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-22 11:02:19.156 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-22 11:02:20.061 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-22 11:02:20.639 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 11:02:20.641 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 11:02:20.642 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-22 11:02:20.821 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 11:02:21.020 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-22 11:02:21.036 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共19个检查项
2025-08-22 11:02:21.324 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 11:02:21.355 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.817 seconds (JVM running for 3.557)
2025-08-22 11:02:34.633 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 11:02:34.803 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: null - 检查项数量: 1
2025-08-22 11:02:37.625 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: null - 检查项数量: 1
2025-08-22 11:02:38.970 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: null - 检查项数量: 1
2025-08-22 11:23:08.901 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 11:23:08.918 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication v1.0-SNAPSHOT using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 19812 (D:\git20250402\aegis\target\aegis-1.0-SNAPSHOT.jar started by Administrator in D:\git20250402\aegis)
2025-08-22 11:23:08.919 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-22 11:23:09.607 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-22 11:23:10.105 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 11:23:10.107 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 11:23:10.108 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-22 11:23:10.213 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 11:23:10.456 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-22 11:23:10.474 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共19个检查项
2025-08-22 11:23:10.736 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 11:23:10.763 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.412 seconds (JVM running for 2.89)
2025-08-22 11:23:34.680 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 11:23:41.732 [http-nio-8080-exec-3] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 11:23:41.733 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:23:42.314 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:23:42.315 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 11:23:42.459 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-08-22 11:23:42.472 [http-nio-8080-exec-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 11:23:42.600 [http-nio-8080-exec-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-22 11:23:42.607 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 11:23:42.876 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-08-22 11:23:48.009 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 11:23:48.009 [http-nio-8080-exec-2] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 11:23:48.014 [http-nio-8080-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-08-22 11:23:48.045 [http-nio-8080-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-08-22 11:45:42.893 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 11:45:42.905 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication v1.0-SNAPSHOT using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 27624 (D:\git20250402\aegis\target\aegis-1.0-SNAPSHOT.jar started by Administrator in D:\git20250402\aegis)
2025-08-22 11:45:42.906 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-22 11:45:43.577 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-22 11:45:44.068 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 11:45:44.070 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 11:45:44.071 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-22 11:45:44.182 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 11:45:44.433 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-22 11:45:44.451 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共29个检查项
2025-08-22 11:45:44.709 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 11:45:44.735 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.326 seconds (JVM running for 2.851)
2025-08-22 11:46:07.245 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 11:46:16.717 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 11:46:16.718 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:46:17.280 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:46:17.281 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 11:46:17.530 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-08-22 11:46:17.542 [http-nio-8080-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 11:46:18.124 [http-nio-8080-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-22 11:46:18.134 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 11:46:18.323 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-08-22 11:46:36.581 [http-nio-8080-exec-5] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 11:46:36.582 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:46:36.650 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:46:36.651 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 11:46:36.789 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 11:46:36.790 [http-nio-8080-exec-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-08-22 11:46:36.801 [http-nio-8080-exec-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-08-22 11:46:36.801 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 11:46:36.954 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 11:46:40.851 [http-nio-8080-exec-6] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 11:46:40.853 [http-nio-8080-exec-6] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 11:46:53.787 [http-nio-8080-exec-7] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 11:46:53.788 [http-nio-8080-exec-7] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 11:46:53.788 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:46:53.875 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:46:53.876 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - date +%s
2025-08-22 11:46:54.062 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 11
2025-08-22 11:46:54.062 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:47:04.130 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:47:04.130 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - date +%s
2025-08-22 11:47:14.276 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 11
2025-08-22 11:47:20.824 [http-nio-8080-exec-8] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 11:47:20.825 [http-nio-8080-exec-8] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 11:47:20.826 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:47:20.939 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:47:20.939 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - date +%s
2025-08-22 11:47:21.119 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 11
2025-08-22 11:47:21.120 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:47:41.234 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:47:41.235 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - date +%s
2025-08-22 11:47:51.386 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 11
2025-08-22 11:49:05.830 [http-nio-8080-exec-9] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 11:49:05.831 [http-nio-8080-exec-9] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 11:49:05.833 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:49:05.909 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:49:05.910 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}')
2025-08-22 11:49:06.274 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 39
2025-08-22 11:49:06.274 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:49:06.367 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:49:06.367 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}')
2025-08-22 11:49:06.733 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 38
2025-08-22 11:49:06.734 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:49:11.796 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:49:11.796 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}')
2025-08-22 11:49:17.159 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 38
2025-08-22 11:49:17.160 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}')
2025-08-22 11:49:17.495 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 38
2025-08-22 11:49:17.496 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}')
2025-08-22 11:49:22.817 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 38
2025-08-22 11:50:17.376 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 11:50:17.377 [http-nio-8080-exec-1] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 11:50:17.377 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:50:17.479 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:50:17.479 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker --version 2>/dev/null || echo 'Docker not installed'
2025-08-22 11:50:17.728 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 39
2025-08-22 11:50:17.728 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:50:37.846 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:50:37.847 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker --version 2>/dev/null || echo 'Docker not installed'
2025-08-22 11:50:53.103 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 39
2025-08-22 11:51:21.687 [http-nio-8080-exec-3] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 11:51:21.687 [http-nio-8080-exec-3] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 11:51:21.688 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:51:21.751 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:51:21.751 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive'
2025-08-22 11:51:21.887 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 7
2025-08-22 11:51:21.888 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 11:51:36.993 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 11:51:36.994 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive'
2025-08-22 11:51:47.232 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 7
2025-08-22 13:30:51.606 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 13:30:51.687 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 35028 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-22 13:30:51.689 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-22 13:30:53.671 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-22 13:30:54.386 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 13:30:54.389 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 13:30:54.390 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-22 13:30:54.606 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 13:30:54.876 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-22 13:30:54.896 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共11个检查项
2025-08-22 13:30:55.351 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 13:30:55.409 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 4.947 seconds (JVM running for 6.016)
2025-08-22 13:30:59.281 [http-nio-8080-exec-4] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 13:31:22.535 [http-nio-8080-exec-5] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 13:31:22.536 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:31:23.136 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:31:23.137 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 13:31:23.285 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 13:31:23.345 [http-nio-8080-exec-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 13:31:23.584 [http-nio-8080-exec-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-22 13:31:23.590 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 13:31:23.713 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 13:31:28.958 [http-nio-8080-exec-7] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 13:31:28.958 [http-nio-8080-exec-7] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 13:31:51.593 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 13:31:51.594 [http-nio-8080-exec-1] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 13:31:51.595 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:31:51.684 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:31:51.684 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}')
2025-08-22 13:31:52.031 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 38
2025-08-22 13:31:52.032 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:31:52.090 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:31:52.091 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}')
2025-08-22 13:31:52.456 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 39
2025-08-22 13:31:52.458 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:31:57.532 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:31:57.533 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}')
2025-08-22 13:32:02.909 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 39
2025-08-22 13:32:02.911 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}')
2025-08-22 13:32:03.272 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 39
2025-08-22 13:32:03.273 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}')
2025-08-22 13:32:23.626 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 38
2025-08-22 13:37:14.439 [http-nio-8080-exec-6] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 13:37:14.439 [http-nio-8080-exec-6] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 13:37:14.440 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:37:14.515 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:37:14.516 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker --version 2>/dev/null || echo 'Docker not installed'
2025-08-22 13:37:14.785 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 39
2025-08-22 13:37:14.785 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:37:19.852 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:37:19.852 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker --version 2>/dev/null || echo 'Docker not installed'
2025-08-22 13:37:30.085 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 39
2025-08-22 13:38:21.563 [http-nio-8080-exec-8] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 13:38:21.564 [http-nio-8080-exec-8] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 13:38:21.564 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:38:21.655 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:38:21.655 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker images | grep device-data-service | awk '{print $2}' | head -1
2025-08-22 13:38:34.489 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 10
2025-08-22 13:38:34.490 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:38:44.583 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:38:44.584 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker images | grep device-data-service | awk '{print $2}' | head -1
2025-08-22 13:39:09.673 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 10
2025-08-22 13:41:11.461 [http-nio-8080-exec-9] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 13:41:11.462 [http-nio-8080-exec-9] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 13:41:11.462 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:41:11.527 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:41:11.528 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive'
2025-08-22 13:41:11.767 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 7
2025-08-22 13:41:11.768 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:41:16.840 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:41:16.840 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive'
2025-08-22 13:41:26.985 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 7
2025-08-22 13:41:52.921 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 13:41:52.922 [http-nio-8080-exec-4] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 13:41:52.923 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:41:53.033 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:41:53.033 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive'
2025-08-22 13:41:53.179 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 18
2025-08-22 13:41:53.179 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:42:13.317 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:42:13.318 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive'
2025-08-22 13:42:33.494 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 7
2025-08-22 13:44:20.379 [http-nio-8080-exec-7] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 13:44:20.380 [http-nio-8080-exec-7] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 13:44:20.383 [http-nio-8080-exec-7] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-08-22 13:44:20.393 [http-nio-8080-exec-7] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-08-22 13:45:14.072 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 13:45:14.072 [http-nio-8080-exec-2] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 13:45:14.107 [http-nio-8080-exec-2] ERROR c.c.d.service.DatabaseService - 执行SQL查询失败: ERROR: relation "pecdeviceextend" does not exist
  位置：41
2025-08-22 13:46:14.707 [http-nio-8080-exec-3] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 13:46:14.707 [http-nio-8080-exec-3] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 13:46:14.712 [http-nio-8080-exec-3] ERROR c.c.d.service.DatabaseService - 执行SQL查询失败: ERROR: relation "thresholdmeasurepoint" does not exist
  位置：22
2025-08-22 13:53:05.774 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 13:53:05.785 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication v1.0-SNAPSHOT using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 24528 (D:\git20250402\aegis\target\aegis-1.0-SNAPSHOT.jar started by Administrator in D:\git20250402\aegis)
2025-08-22 13:53:05.785 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-22 13:53:06.454 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-22 13:53:07.044 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 13:53:07.046 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 13:53:07.047 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-22 13:53:07.161 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 13:53:07.399 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-22 13:53:07.415 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共11个检查项
2025-08-22 13:53:07.773 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 13:53:07.807 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.552 seconds (JVM running for 3.157)
2025-08-22 13:53:15.964 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 13:53:37.151 [http-nio-8080-exec-7] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 13:53:37.152 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:53:37.680 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:53:37.680 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 13:53:37.825 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 13:53:37.842 [http-nio-8080-exec-7] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 13:53:38.028 [http-nio-8080-exec-7] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-22 13:53:38.035 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 13:53:38.174 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 13:53:42.241 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 13:53:42.242 [http-nio-8080-exec-1] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 13:53:46.637 [http-nio-8080-exec-3] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 13:53:46.638 [http-nio-8080-exec-3] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 13:53:46.639 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:53:46.715 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:53:46.716 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - date +%s
2025-08-22 13:53:46.868 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 11
2025-08-22 13:53:46.869 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:53:56.933 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:53:56.935 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - date +%s
2025-08-22 13:54:07.104 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 11
2025-08-22 13:54:26.467 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 13:54:26.468 [http-nio-8080-exec-4] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 13:54:26.469 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:54:26.533 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:54:26.534 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - date +%s
2025-08-22 13:54:26.666 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 11
2025-08-22 13:54:26.667 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:54:31.758 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:54:31.758 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - date +%s
2025-08-22 13:54:36.900 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 11
2025-08-22 13:54:44.660 [http-nio-8080-exec-5] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 13:54:44.661 [http-nio-8080-exec-5] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 13:54:44.662 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:54:44.739 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:54:44.740 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{printf "Memory:%s/%s(%.1f%%);",$3,$2,$3*100/$2}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}'
2025-08-22 13:54:45.121 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 55
2025-08-22 13:54:45.123 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:54:45.207 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:54:45.208 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{printf "Memory:%s/%s(%.1f%%);",$3,$2,$3*100/$2}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}'
2025-08-22 13:54:45.545 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 55
2025-08-22 13:54:45.546 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:54:50.627 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:54:50.627 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{printf "Memory:%s/%s(%.1f%%);",$3,$2,$3*100/$2}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}'
2025-08-22 13:55:00.975 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 54
2025-08-22 13:55:00.976 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{printf "Memory:%s/%s(%.1f%%);",$3,$2,$3*100/$2}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}'
2025-08-22 13:55:01.374 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 55
2025-08-22 13:55:01.376 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{printf "Memory:%s/%s(%.1f%%);",$3,$2,$3*100/$2}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}'
2025-08-22 13:55:06.733 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 54
2025-08-22 13:57:08.357 [http-nio-8080-exec-9] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 13:57:08.358 [http-nio-8080-exec-9] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 13:57:08.359 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:57:08.435 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:57:08.436 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort
2025-08-22 13:57:08.674 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 1989
2025-08-22 13:57:08.675 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 13:57:18.744 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 13:57:18.744 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort
2025-08-22 13:57:29.002 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 1748
2025-08-22 14:01:06.070 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 14:01:06.071 [http-nio-8080-exec-2] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 14:01:06.072 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 14:01:06.186 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 14:01:06.187 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort
2025-08-22 14:01:06.446 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 1989
2025-08-22 14:01:06.447 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 14:01:16.520 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 14:01:16.521 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort
2025-08-22 14:01:21.759 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 1748
2025-08-22 14:01:29.631 [http-nio-8080-exec-7] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 14:01:29.631 [http-nio-8080-exec-7] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 14:01:29.632 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 14:01:29.715 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 14:01:29.716 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep device-data-service
2025-08-22 14:01:29.963 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 55
2025-08-22 14:01:29.964 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 14:01:40.037 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 14:01:40.038 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep device-data-service
2025-08-22 14:01:45.304 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 55
2025-08-22 14:02:16.465 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 14:02:16.466 [http-nio-8080-exec-1] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 14:02:16.466 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 14:02:16.566 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 14:02:16.566 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive'
2025-08-22 14:02:16.695 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 7
2025-08-22 14:02:16.695 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 14:02:26.771 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 14:02:26.772 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive'
2025-08-22 14:02:31.929 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 7
2025-08-22 14:02:38.633 [http-nio-8080-exec-3] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 14:02:38.633 [http-nio-8080-exec-3] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 14:02:38.635 [http-nio-8080-exec-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-08-22 14:02:38.648 [http-nio-8080-exec-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-08-22 14:02:52.484 [http-nio-8080-exec-8] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 14:02:52.486 [http-nio-8080-exec-8] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 14:03:05.702 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 14:03:05.702 [http-nio-8080-exec-4] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:09:32.817 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 17:09:32.833 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication v1.0-SNAPSHOT using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 28276 (D:\git20250402\aegis\target\aegis-1.0-SNAPSHOT.jar started by Administrator in D:\git20250402\aegis)
2025-08-22 17:09:32.834 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-22 17:09:33.713 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-22 17:09:34.199 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 17:09:34.201 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 17:09:34.201 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-22 17:09:34.313 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 17:09:34.562 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-22 17:09:34.584 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共19个检查项
2025-08-22 17:09:34.843 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 17:09:34.870 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.574 seconds (JVM running for 3.131)
2025-08-22 17:09:41.023 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 17:10:00.337 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 17:10:00.338 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:10:01.032 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:10:01.033 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 17:10:01.189 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 17:10:01.201 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 17:10:01.326 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-22 17:10:01.334 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 17:10:01.477 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 17:10:27.400 [http-nio-8080-exec-3] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:10:27.401 [http-nio-8080-exec-3] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:10:31.189 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:10:31.203 [http-nio-8080-exec-4] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:10:31.206 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:10:31.293 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:10:31.293 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep -A 20 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'cet_auth-push'
2025-08-22 17:10:31.417 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-08-22 17:13:22.529 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 17:13:22.544 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 24208 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-22 17:13:22.545 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-22 17:13:23.335 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-22 17:13:23.873 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 17:13:23.877 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 17:13:23.877 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-22 17:13:24.074 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 17:13:24.280 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-22 17:13:24.300 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共19个检查项
2025-08-22 17:13:24.605 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 17:13:24.634 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.728 seconds (JVM running for 3.252)
2025-08-22 17:13:30.181 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 17:13:50.424 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 17:13:50.425 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:13:50.981 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:13:50.982 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 17:13:51.155 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 17:13:51.171 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 17:13:51.300 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-22 17:13:51.307 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 17:13:51.463 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 17:13:58.621 [http-nio-8080-exec-3] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:13:58.621 [http-nio-8080-exec-3] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:13:58.622 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:13:58.766 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:13:58.767 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep -A 100 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'cet_auth-push'
2025-08-22 17:13:58.920 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 29
2025-08-22 17:14:13.475 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:14:13.475 [http-nio-8080-exec-4] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:14:13.476 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:14:13.563 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:14:13.563 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep -A 20 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'sms_swtich'
2025-08-22 17:14:13.721 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-08-22 17:17:49.355 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 17:17:49.367 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 35536 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-22 17:17:49.367 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-22 17:17:50.125 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-22 17:17:50.615 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 17:17:50.618 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 17:17:50.618 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-22 17:17:50.797 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 17:17:51.006 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-22 17:17:51.027 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共19个检查项
2025-08-22 17:17:51.326 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 17:17:51.356 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.66 seconds (JVM running for 3.127)
2025-08-22 17:18:01.698 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 17:18:18.671 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-22 17:18:18.671 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:18:19.313 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:18:19.314 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 17:18:19.455 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 17:18:19.472 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 17:18:19.621 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-22 17:18:19.630 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-22 17:18:19.761 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5022
2025-08-22 17:18:26.579 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:18:26.580 [http-nio-8080-exec-4] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:18:26.581 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:18:26.718 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:18:26.719 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'sms_swtich'
2025-08-22 17:18:26.861 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 26
2025-08-22 17:18:31.850 [http-nio-8080-exec-3] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:18:31.850 [http-nio-8080-exec-3] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:18:31.850 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:18:31.935 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:18:31.935 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - ls /etc/CET/only-report/config/ 2>/dev/null || echo 'directory not found'
2025-08-22 17:18:32.095 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 46
2025-08-22 17:18:45.113 [http-nio-8080-exec-5] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:18:45.113 [http-nio-8080-exec-5] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:18:45.113 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:18:50.182 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:18:50.182 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'cet_auth-push'
2025-08-22 17:18:50.313 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 29
2025-08-22 17:18:59.881 [http-nio-8080-exec-8] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:18:59.881 [http-nio-8080-exec-8] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:18:59.881 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:19:04.973 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:19:04.974 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep -n $'\t' /etc/CET/docker/docker-compose.yml 2>/dev/null || echo 'no tabs found'
2025-08-22 17:19:15.143 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 14
2025-08-22 17:19:22.762 [http-nio-8080-exec-10] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:19:22.762 [http-nio-8080-exec-10] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:19:22.762 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:19:22.847 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:19:22.847 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - date +%s
2025-08-22 17:19:22.995 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 11
2025-08-22 17:19:22.995 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:19:43.076 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:19:43.076 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - date +%s
2025-08-22 17:20:03.240 [http-nio-8080-exec-10] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 11
2025-08-22 17:20:07.676 [http-nio-8080-exec-7] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:20:07.676 [http-nio-8080-exec-7] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:20:07.676 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:20:07.793 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:20:07.793 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - date +%s
2025-08-22 17:20:07.921 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 11
2025-08-22 17:20:07.922 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:20:28.014 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:20:28.015 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - date +%s
2025-08-22 17:20:48.158 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 11
2025-08-22 17:20:55.970 [http-nio-8080-exec-6] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:20:55.971 [http-nio-8080-exec-6] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:20:55.971 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:20:56.066 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:20:56.067 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}'
2025-08-22 17:20:56.431 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 55
2025-08-22 17:20:56.434 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:20:56.504 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:20:56.504 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}'
2025-08-22 17:20:56.861 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 55
2025-08-22 17:20:56.861 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:21:01.924 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:21:01.924 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}'
2025-08-22 17:21:12.279 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 54
2025-08-22 17:21:12.280 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}'
2025-08-22 17:21:12.648 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 56
2025-08-22 17:21:12.648 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}'
2025-08-22 17:21:23.039 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 54
2025-08-22 17:21:44.087 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:21:44.087 [http-nio-8080-exec-2] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:21:44.087 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:21:44.200 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:21:44.201 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - ssh -o BatchMode=yes -o ConnectTimeout=5 ************ 'echo success' 2>/dev/null || echo 'failed'
2025-08-22 17:21:54.570 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 8
2025-08-22 17:21:54.570 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:22:04.643 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:22:04.643 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - ssh -o BatchMode=yes -o ConnectTimeout=5 ************ 'echo success' 2>/dev/null || echo 'failed'
2025-08-22 17:22:14.877 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 8
2025-08-22 17:22:24.740 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:22:24.740 [http-nio-8080-exec-1] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:22:24.741 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:22:24.844 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:22:24.845 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - ls -1 /var/cache/CET/filemanager/ 2>/dev/null | sort
2025-08-22 17:22:24.984 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 154
2025-08-22 17:22:24.985 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:22:35.089 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:22:35.090 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - ls -1 /var/cache/CET/filemanager/ 2>/dev/null | sort
2025-08-22 17:22:50.207 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 154
2025-08-22 17:22:54.114 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:22:54.114 [http-nio-8080-exec-4] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:22:54.114 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:22:54.184 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:22:54.185 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort
2025-08-22 17:22:54.433 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 1989
2025-08-22 17:22:54.433 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:22:59.510 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:22:59.511 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort
2025-08-22 17:23:09.747 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 1748
2025-08-22 17:25:08.750 [http-nio-8080-exec-5] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:25:08.750 [http-nio-8080-exec-5] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:25:08.750 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:25:08.826 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:25:08.826 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort
2025-08-22 17:25:09.073 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 1989
2025-08-22 17:25:09.074 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:25:24.155 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:25:24.155 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort
2025-08-22 17:25:24.592 [http-nio-8080-exec-9] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:25:24.592 [http-nio-8080-exec-9] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:25:24.592 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:25:24.689 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:25:24.690 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep device-data-service
2025-08-22 17:25:29.936 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 55
2025-08-22 17:25:29.937 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:25:34.388 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 1748
2025-08-22 17:25:40.011 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:25:40.012 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep device-data-service
2025-08-22 17:26:27.210 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 17:26:27.226 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 19336 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-22 17:26:27.228 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-22 17:26:28.012 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-22 17:26:28.523 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 17:26:28.526 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 17:26:28.526 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-22 17:26:28.704 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 17:26:28.907 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-22 17:26:28.926 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共19个检查项
2025-08-22 17:26:29.228 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 17:26:29.258 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.666 seconds (JVM running for 3.194)
2025-08-22 17:26:58.644 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-22 17:26:58.803 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:26:58.804 [http-nio-8080-exec-2] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:26:58.805 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:26:59.337 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:26:59.338 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep device-data-service
2025-08-22 17:26:59.590 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 55
2025-08-22 17:26:59.592 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:27:04.659 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:27:04.659 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep device-data-service
2025-08-22 17:27:14.897 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 55
2025-08-22 17:27:59.380 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:27:59.380 [http-nio-8080-exec-1] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:27:59.380 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:27:59.471 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:27:59.472 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive'
2025-08-22 17:27:59.611 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 7
2025-08-22 17:27:59.611 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:28:09.726 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:28:09.727 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive'
2025-08-22 17:28:19.868 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 7
2025-08-22 17:28:22.745 [http-nio-8080-exec-3] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:28:22.745 [http-nio-8080-exec-3] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:28:22.745 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:28:22.854 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:28:22.854 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format '{{.Names}}' | head -10
2025-08-22 17:28:23.121 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 251
2025-08-22 17:28:23.121 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-model-service-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-22 17:28:23.455 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 944
2025-08-22 17:28:23.455 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-bff-service-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-22 17:28:23.791 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-08-22 17:28:23.791 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 soda_lowcode-soda-bff-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-22 17:28:24.141 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 2522
2025-08-22 17:28:24.142 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 soda_lowcode-soda-web-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-22 17:28:24.384 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-08-22 17:28:24.384 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 soda_lowcode-soda-redis-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-22 17:28:29.628 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-08-22 17:28:29.629 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 sodalowcode-soda-redis-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-22 17:28:29.888 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-08-22 17:28:29.888 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 soda_lowcode_mongodb_primary 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-22 17:28:30.133 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-08-22 17:28:30.134 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-dcim-web-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-22 17:28:30.484 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-08-22 17:28:30.485 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-only-report-docker-service-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-22 17:28:30.728 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-08-22 17:28:30.728 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-service-monitor-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5
2025-08-22 17:28:30.956 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 83
2025-08-22 17:28:38.842 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:28:38.842 [http-nio-8080-exec-4] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:28:38.843 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:28:38.929 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:28:38.929 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep netclient
2025-08-22 17:28:39.204 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 50
2025-08-22 17:28:39.205 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-22 17:28:54.327 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-22 17:28:54.327 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep netclient
2025-08-22 17:29:04.583 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 50
2025-08-22 17:29:17.401 [http-nio-8080-exec-5] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:29:17.401 [http-nio-8080-exec-5] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:29:17.419 [http-nio-8080-exec-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-22 17:29:17.596 [http-nio-8080-exec-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-22 17:29:17.604 [http-nio-8080-exec-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-08-22 17:29:17.619 [http-nio-8080-exec-5] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Start completed.
2025-08-22 17:29:23.103 [http-nio-8080-exec-7] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:29:23.103 [http-nio-8080-exec-7] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:29:32.819 [http-nio-8080-exec-6] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-08-22 17:29:32.824 [http-nio-8080-exec-6] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-08-22 17:32:42.432 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-22 17:32:42.443 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 20108 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-22 17:32:42.444 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-22 17:32:43.227 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-22 17:32:43.859 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-22 17:32:43.862 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-22 17:32:43.862 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-22 17:32:44.110 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-22 17:32:44.430 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-22 17:32:44.450 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共19个检查项
2025-08-22 17:32:44.765 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-22 17:32:44.801 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.959 seconds (JVM running for 3.472)
