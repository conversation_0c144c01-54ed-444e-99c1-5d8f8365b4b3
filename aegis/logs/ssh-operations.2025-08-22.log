2025-08-22 10:27:50.237 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-08-22 10:27:50.510 - 服务器: 10.12.135.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3325
2025-08-22 10:27:50.923 - 服务器: 10.12.135.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3325
2025-08-22 10:31:36.367 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-08-22 10:31:36.524 - 服务器: 10.12.135.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3325
2025-08-22 10:31:36.770 - 服务器: 10.12.135.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3325
2025-08-22 10:31:38.884 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-08-22 10:31:39.178 - 服务器: 10.12.135.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3325
2025-08-22 10:31:39.367 - 服务器: 10.12.135.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3325
2025-08-22 10:37:54.955 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-08-22 10:37:55.119 - 服务器: 10.12.135.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3325
2025-08-22 10:37:55.288 - 服务器: 10.12.135.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3325
2025-08-22 10:40:55.822 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 10:40:55.988 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 10:40:56.142 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 10:41:04.558 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 10:41:04.706 - 服务器: 10.12.137.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 10:41:04.843 - 服务器: 10.12.137.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 10:41:16.102 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 10:41:21.247 - 服务器: 10.12.137.13, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 4945
2025-08-22 10:41:31.463 - 服务器: 10.12.137.13, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 4945
2025-08-22 11:01:00.033 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 11:01:00.166 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 11:01:00.501 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 11:01:16.386 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 11:01:16.535 - 服务器: 10.12.137.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 11:01:16.688 - 服务器: 10.12.137.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 11:01:38.485 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 11:01:58.627 - 服务器: 10.12.137.13, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 4945
2025-08-22 11:23:42.305 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-08-22 11:23:42.459 - 服务器: 10.12.135.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3325
2025-08-22 11:23:42.876 - 服务器: 10.12.135.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3325
2025-08-22 11:46:17.271 - SSH连接测试成功: 服务器=10.12.135.12, 地址=10.12.135.12:22
2025-08-22 11:46:17.529 - 服务器: 10.12.135.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3325
2025-08-22 11:46:18.323 - 服务器: 10.12.135.12, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 3325
2025-08-22 11:46:36.650 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 11:46:36.788 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 11:46:36.954 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 11:46:53.874 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 11:46:54.062 - 服务器: 10.12.137.12, 命令: date +%s, 退出码: 0, 输出长度: 11
2025-08-22 11:47:04.129 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 11:47:14.275 - 服务器: 10.12.137.13, 命令: date +%s, 退出码: 0, 输出长度: 11
2025-08-22 11:47:20.938 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 11:47:21.119 - 服务器: 10.12.137.12, 命令: date +%s, 退出码: 0, 输出长度: 11
2025-08-22 11:47:41.234 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 11:47:51.386 - 服务器: 10.12.137.13, 命令: date +%s, 退出码: 0, 输出长度: 11
2025-08-22 11:49:05.908 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 11:49:06.273 - 服务器: 10.12.137.14, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}'), 退出码: 0, 输出长度: 39
2025-08-22 11:49:06.366 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 11:49:06.733 - 服务器: 10.12.137.12, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}'), 退出码: 0, 输出长度: 38
2025-08-22 11:49:11.796 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 11:49:17.159 - 服务器: 10.12.137.13, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}'), 退出码: 0, 输出长度: 38
2025-08-22 11:49:17.495 - 服务器: 10.12.137.12, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}'), 退出码: 0, 输出长度: 38
2025-08-22 11:49:22.817 - 服务器: 10.12.137.13, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}'), 退出码: 0, 输出长度: 38
2025-08-22 11:50:17.478 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 11:50:17.727 - 服务器: 10.12.137.12, 命令: docker --version 2>/dev/null || echo 'Docker not installed', 退出码: 0, 输出长度: 39
2025-08-22 11:50:37.845 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 11:50:53.103 - 服务器: 10.12.137.13, 命令: docker --version 2>/dev/null || echo 'Docker not installed', 退出码: 0, 输出长度: 39
2025-08-22 11:51:21.751 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 11:51:21.887 - 服务器: 10.12.137.12, 命令: systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive', 退出码: 0, 输出长度: 7
2025-08-22 11:51:36.993 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 11:51:47.232 - 服务器: 10.12.137.13, 命令: systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive', 退出码: 0, 输出长度: 7
2025-08-22 13:31:23.124 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 13:31:23.285 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 13:31:23.713 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 13:31:51.682 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 13:31:52.031 - 服务器: 10.12.137.14, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}'), 退出码: 0, 输出长度: 38
2025-08-22 13:31:52.090 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 13:31:52.456 - 服务器: 10.12.137.12, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}'), 退出码: 0, 输出长度: 39
2025-08-22 13:31:57.532 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 13:32:02.908 - 服务器: 10.12.137.13, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}'), 退出码: 0, 输出长度: 39
2025-08-22 13:32:03.272 - 服务器: 10.12.137.12, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}'), 退出码: 0, 输出长度: 39
2025-08-22 13:32:23.626 - 服务器: 10.12.137.13, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%; Memory:' $(free | awk 'NR==2{printf "%.1f", $3*100/$2}') '%; Disk:' $(df -h / | awk 'NR==2{print $5}'), 退出码: 0, 输出长度: 38
2025-08-22 13:37:14.515 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 13:37:14.784 - 服务器: 10.12.137.12, 命令: docker --version 2>/dev/null || echo 'Docker not installed', 退出码: 0, 输出长度: 39
2025-08-22 13:37:19.851 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 13:37:30.085 - 服务器: 10.12.137.13, 命令: docker --version 2>/dev/null || echo 'Docker not installed', 退出码: 0, 输出长度: 39
2025-08-22 13:38:21.655 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 13:38:34.489 - 服务器: 10.12.137.12, 命令: docker images | grep device-data-service | awk '{print $2}' | head -1, 退出码: 0, 输出长度: 10
2025-08-22 13:38:44.583 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 13:39:09.673 - 服务器: 10.12.137.13, 命令: docker images | grep device-data-service | awk '{print $2}' | head -1, 退出码: 0, 输出长度: 10
2025-08-22 13:41:11.527 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 13:41:11.767 - 服务器: 10.12.137.12, 命令: systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive', 退出码: 0, 输出长度: 7
2025-08-22 13:41:16.839 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 13:41:26.985 - 服务器: 10.12.137.13, 命令: systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive', 退出码: 0, 输出长度: 7
2025-08-22 13:41:53.032 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 13:41:53.178 - 服务器: 10.12.137.12, 命令: systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive', 退出码: 0, 输出长度: 18
2025-08-22 13:42:13.317 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 13:42:33.493 - 服务器: 10.12.137.13, 命令: systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive', 退出码: 0, 输出长度: 7
2025-08-22 13:53:37.673 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 13:53:37.824 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 13:53:38.174 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 13:53:46.714 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 13:53:46.868 - 服务器: 10.12.137.12, 命令: date +%s, 退出码: 0, 输出长度: 11
2025-08-22 13:53:56.933 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 13:54:07.103 - 服务器: 10.12.137.13, 命令: date +%s, 退出码: 0, 输出长度: 11
2025-08-22 13:54:26.533 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 13:54:26.665 - 服务器: 10.12.137.12, 命令: date +%s, 退出码: 0, 输出长度: 11
2025-08-22 13:54:31.757 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 13:54:36.899 - 服务器: 10.12.137.13, 命令: date +%s, 退出码: 0, 输出长度: 11
2025-08-22 13:54:44.739 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 13:54:45.121 - 服务器: 10.12.137.14, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{printf "Memory:%s/%s(%.1f%%);",$3,$2,$3*100/$2}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}', 退出码: 0, 输出长度: 55
2025-08-22 13:54:45.207 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 13:54:45.545 - 服务器: 10.12.137.12, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{printf "Memory:%s/%s(%.1f%%);",$3,$2,$3*100/$2}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}', 退出码: 0, 输出长度: 55
2025-08-22 13:54:50.626 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 13:55:00.975 - 服务器: 10.12.137.13, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{printf "Memory:%s/%s(%.1f%%);",$3,$2,$3*100/$2}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}', 退出码: 0, 输出长度: 54
2025-08-22 13:55:01.374 - 服务器: 10.12.137.12, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{printf "Memory:%s/%s(%.1f%%);",$3,$2,$3*100/$2}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}', 退出码: 0, 输出长度: 55
2025-08-22 13:55:06.733 - 服务器: 10.12.137.13, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{printf "Memory:%s/%s(%.1f%%);",$3,$2,$3*100/$2}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}', 退出码: 0, 输出长度: 54
2025-08-22 13:57:08.434 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 13:57:08.674 - 服务器: 10.12.137.12, 命令: docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort, 退出码: 0, 输出长度: 1989
2025-08-22 13:57:18.743 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 13:57:29.002 - 服务器: 10.12.137.13, 命令: docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort, 退出码: 0, 输出长度: 1748
2025-08-22 14:01:06.186 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 14:01:06.446 - 服务器: 10.12.137.12, 命令: docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort, 退出码: 0, 输出长度: 1989
2025-08-22 14:01:16.520 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 14:01:21.758 - 服务器: 10.12.137.13, 命令: docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort, 退出码: 0, 输出长度: 1748
2025-08-22 14:01:29.713 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 14:01:29.963 - 服务器: 10.12.137.12, 命令: docker ps --format 'table {{.Image}}' | grep device-data-service, 退出码: 0, 输出长度: 55
2025-08-22 14:01:40.037 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 14:01:45.304 - 服务器: 10.12.137.13, 命令: docker ps --format 'table {{.Image}}' | grep device-data-service, 退出码: 0, 输出长度: 55
2025-08-22 14:02:16.565 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 14:02:16.695 - 服务器: 10.12.137.12, 命令: systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive', 退出码: 0, 输出长度: 7
2025-08-22 14:02:26.771 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 14:02:31.929 - 服务器: 10.12.137.13, 命令: systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive', 退出码: 0, 输出长度: 7
2025-08-22 17:10:01.021 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 17:10:01.188 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 17:10:01.477 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 17:10:31.291 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 17:10:31.416 - 服务器: 10.12.137.14, 命令: grep -A 20 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'cet_auth-push', 退出码: 1, 输出长度: 0
2025-08-22 17:13:50.968 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 17:13:51.154 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 17:13:51.463 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 17:13:58.765 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 17:13:58.919 - 服务器: 10.12.137.14, 命令: grep -A 100 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'cet_auth-push', 退出码: 0, 输出长度: 29
2025-08-22 17:14:13.562 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 17:14:13.721 - 服务器: 10.12.137.14, 命令: grep -A 20 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'sms_swtich', 退出码: 1, 输出长度: 0
2025-08-22 17:18:19.287 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 17:18:19.455 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 17:18:19.761 - 服务器: 10.12.137.14, 命令: cat /etc/CET/docker/.env, 退出码: 0, 输出长度: 5022
2025-08-22 17:18:26.717 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 17:18:26.861 - 服务器: 10.12.137.14, 命令: grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'sms_swtich', 退出码: 0, 输出长度: 26
2025-08-22 17:18:31.935 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 17:18:32.093 - 服务器: 10.12.137.14, 命令: ls /etc/CET/only-report/config/ 2>/dev/null || echo 'directory not found', 退出码: 0, 输出长度: 46
2025-08-22 17:18:50.182 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 17:18:50.313 - 服务器: 10.12.137.14, 命令: grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'cet_auth-push', 退出码: 0, 输出长度: 29
2025-08-22 17:19:04.972 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 17:19:15.142 - 服务器: 10.12.137.14, 命令: grep -n $'\t' /etc/CET/docker/docker-compose.yml 2>/dev/null || echo 'no tabs found', 退出码: 0, 输出长度: 14
2025-08-22 17:19:22.846 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 17:19:22.993 - 服务器: 10.12.137.12, 命令: date +%s, 退出码: 0, 输出长度: 11
2025-08-22 17:19:43.075 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 17:20:03.239 - 服务器: 10.12.137.13, 命令: date +%s, 退出码: 0, 输出长度: 11
2025-08-22 17:20:07.792 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 17:20:07.921 - 服务器: 10.12.137.12, 命令: date +%s, 退出码: 0, 输出长度: 11
2025-08-22 17:20:28.014 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 17:20:48.158 - 服务器: 10.12.137.13, 命令: date +%s, 退出码: 0, 输出长度: 11
2025-08-22 17:20:56.066 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 17:20:56.431 - 服务器: 10.12.137.14, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}', 退出码: 0, 输出长度: 55
2025-08-22 17:20:56.503 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 17:20:56.860 - 服务器: 10.12.137.12, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}', 退出码: 0, 输出长度: 55
2025-08-22 17:21:01.923 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 17:21:12.278 - 服务器: 10.12.137.13, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}', 退出码: 0, 输出长度: 54
2025-08-22 17:21:12.647 - 服务器: 10.12.137.12, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}', 退出码: 0, 输出长度: 56
2025-08-22 17:21:23.038 - 服务器: 10.12.137.13, 命令: echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf "Memory:%s/%s(%.1f%%);",$3,$2,used_percent}' && df -h /var | awk 'NR==2{printf "Disk:%s/%s(%s);",$3,$2,$5}', 退出码: 0, 输出长度: 54
2025-08-22 17:21:44.200 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 17:21:54.569 - 服务器: 10.12.137.12, 命令: ssh -o BatchMode=yes -o ConnectTimeout=5 10.12.137.13 'echo success' 2>/dev/null || echo 'failed', 退出码: 0, 输出长度: 8
2025-08-22 17:22:04.643 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 17:22:14.876 - 服务器: 10.12.137.13, 命令: ssh -o BatchMode=yes -o ConnectTimeout=5 10.12.137.12 'echo success' 2>/dev/null || echo 'failed', 退出码: 0, 输出长度: 8
2025-08-22 17:22:24.844 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 17:22:24.984 - 服务器: 10.12.137.12, 命令: ls -1 /var/cache/CET/filemanager/ 2>/dev/null | sort, 退出码: 0, 输出长度: 154
2025-08-22 17:22:35.089 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 17:22:50.206 - 服务器: 10.12.137.13, 命令: ls -1 /var/cache/CET/filemanager/ 2>/dev/null | sort, 退出码: 0, 输出长度: 154
2025-08-22 17:22:54.184 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 17:22:54.432 - 服务器: 10.12.137.12, 命令: docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort, 退出码: 0, 输出长度: 1989
2025-08-22 17:22:59.510 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 17:23:09.747 - 服务器: 10.12.137.13, 命令: docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort, 退出码: 0, 输出长度: 1748
2025-08-22 17:25:08.825 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 17:25:09.073 - 服务器: 10.12.137.12, 命令: docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort, 退出码: 0, 输出长度: 1989
2025-08-22 17:25:24.155 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 17:25:24.689 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 17:25:29.936 - 服务器: 10.12.137.12, 命令: docker ps --format 'table {{.Image}}' | grep device-data-service, 退出码: 0, 输出长度: 55
2025-08-22 17:25:34.388 - 服务器: 10.12.137.13, 命令: docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort, 退出码: 0, 输出长度: 1748
2025-08-22 17:25:40.011 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 17:26:59.329 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 17:26:59.589 - 服务器: 10.12.137.12, 命令: docker ps --format 'table {{.Image}}' | grep device-data-service, 退出码: 0, 输出长度: 55
2025-08-22 17:27:04.658 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 17:27:14.897 - 服务器: 10.12.137.13, 命令: docker ps --format 'table {{.Image}}' | grep device-data-service, 退出码: 0, 输出长度: 55
2025-08-22 17:27:59.471 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 17:27:59.609 - 服务器: 10.12.137.12, 命令: systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive', 退出码: 0, 输出长度: 7
2025-08-22 17:28:09.726 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 17:28:19.867 - 服务器: 10.12.137.13, 命令: systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive', 退出码: 0, 输出长度: 7
2025-08-22 17:28:22.853 - SSH连接测试成功: 服务器=10.12.137.14, 地址=10.12.137.14:22
2025-08-22 17:28:23.121 - 服务器: 10.12.137.14, 命令: docker ps --format '{{.Names}}' | head -10, 退出码: 0, 输出长度: 251
2025-08-22 17:28:23.454 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 docker-model-service-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 944
2025-08-22 17:28:23.790 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 docker-bff-service-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 0
2025-08-22 17:28:24.140 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 soda_lowcode-soda-bff-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 2522
2025-08-22 17:28:24.383 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 soda_lowcode-soda-web-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 0
2025-08-22 17:28:29.628 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 soda_lowcode-soda-redis-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 0
2025-08-22 17:28:29.888 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 sodalowcode-soda-redis-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 0
2025-08-22 17:28:30.133 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 soda_lowcode_mongodb_primary 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 0
2025-08-22 17:28:30.484 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 docker-dcim-web-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 0
2025-08-22 17:28:30.727 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 docker-only-report-docker-service-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 0
2025-08-22 17:28:30.956 - 服务器: 10.12.137.14, 命令: docker logs --tail 500 docker-service-monitor-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5, 退出码: 0, 输出长度: 83
2025-08-22 17:28:38.928 - SSH连接测试成功: 服务器=10.12.137.12, 地址=10.12.137.12:22
2025-08-22 17:28:39.204 - 服务器: 10.12.137.12, 命令: docker ps --format 'table {{.Image}}' | grep netclient, 退出码: 0, 输出长度: 50
2025-08-22 17:28:54.326 - SSH连接测试成功: 服务器=10.12.137.13, 地址=10.12.137.13:22
2025-08-22 17:29:04.583 - 服务器: 10.12.137.13, 命令: docker ps --format 'table {{.Image}}' | grep netclient, 退出码: 0, 输出长度: 50
