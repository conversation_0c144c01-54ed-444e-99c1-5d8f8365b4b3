2025-08-21 10:08:39.435 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 10:08:39.535 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 15888 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-21 10:08:39.536 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 10:08:41.506 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 10:08:42.179 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 10:08:42.180 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 10:08:42.181 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 10:08:42.420 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 10:08:42.748 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 10:08:42.762 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 10:08:43.194 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 10:08:43.232 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 5.02 seconds (JVM running for 6.747)
2025-08-21 10:16:22.019 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 10:22:12.419 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 10:22:12.429 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 17944 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-21 10:22:12.430 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 10:22:13.150 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 10:22:13.622 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 10:22:13.625 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 10:22:13.626 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 10:22:13.802 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 10:22:13.988 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 10:22:14.000 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 10:22:14.309 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 10:22:14.342 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.543 seconds (JVM running for 2.991)
2025-08-21 10:22:27.338 [http-nio-8080-exec-4] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 10:24:20.349 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 10:24:20.360 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 27948 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-21 10:24:20.361 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 10:24:21.195 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 10:24:21.853 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 10:24:21.856 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 10:24:21.856 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 10:24:22.043 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 10:24:22.235 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 10:24:22.247 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 10:24:22.530 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 10:24:22.558 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.83 seconds (JVM running for 3.303)
2025-08-21 10:24:28.081 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 10:25:35.998 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 10:25:36.009 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 33892 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-21 10:25:36.010 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 10:25:36.702 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 10:25:37.179 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 10:25:37.183 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 10:25:37.183 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 10:25:37.373 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 10:25:37.557 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 10:25:37.568 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 10:25:37.855 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 10:25:37.887 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.499 seconds (JVM running for 2.921)
2025-08-21 10:25:40.389 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 10:28:21.092 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 10:28:21.103 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 25220 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-21 10:28:21.104 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 10:28:21.791 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 10:28:22.337 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 10:28:22.341 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 10:28:22.342 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 10:28:22.534 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 10:28:22.727 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 10:28:22.739 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 10:28:23.018 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 10:28:23.052 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.593 seconds (JVM running for 3.098)
2025-08-21 10:29:11.974 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 10:29:45.383 [http-nio-8080-exec-4] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> com.cet.datacenter.controller.ServerCheckController.testConnection(com.cet.datacenter.entity.ServerInfo): [Field error in object 'serverInfo' on field 'name': rejected value [null]; codes [NotBlank.serverInfo.name,NotBlank.name,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [serverInfo.name,name]; arguments []; default message [name]]; default message [服务器名称不能为空]] ]
2025-08-21 10:30:23.843 [http-nio-8080-exec-6] WARN  o.s.w.s.m.s.DefaultHandlerExceptionResolver - Resolved [org.springframework.web.bind.MethodArgumentNotValidException: Validation failed for argument [0] in public org.springframework.http.ResponseEntity<java.util.Map<java.lang.String, java.lang.Object>> com.cet.datacenter.controller.ServerCheckController.testConnection(com.cet.datacenter.entity.ServerInfo): [Field error in object 'serverInfo' on field 'name': rejected value [null]; codes [NotBlank.serverInfo.name,NotBlank.name,NotBlank.java.lang.String,NotBlank]; arguments [org.springframework.context.support.DefaultMessageSourceResolvable: codes [serverInfo.name,name]; arguments []; default message [name]]; default message [服务器名称不能为空]] ]
2025-08-21 10:33:33.179 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 10:33:33.188 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 31960 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-21 10:33:33.189 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 10:33:33.958 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 10:33:34.535 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 10:33:34.538 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 10:33:34.538 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 10:33:34.742 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 10:33:34.937 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 10:33:34.953 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 10:33:35.264 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 10:33:35.291 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.752 seconds (JVM running for 3.197)
2025-08-21 10:33:38.885 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 10:33:47.895 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-21 10:33:47.896 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-21 10:33:48.521 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-21 10:34:14.754 [http-nio-8080-exec-8] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: ************ - 检查项数量: 1
2025-08-21 10:50:17.463 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 10:50:17.475 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 33436 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-21 10:50:17.476 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 10:50:18.231 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 10:50:18.733 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 10:50:18.736 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 10:50:18.736 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 10:50:18.907 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 10:50:19.086 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 10:50:19.098 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 10:50:19.391 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 10:50:19.418 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.638 seconds (JVM running for 3.112)
2025-08-21 10:50:25.252 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 10:50:39.547 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-21 10:50:39.548 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-21 10:50:40.041 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-21 10:50:43.830 [http-nio-8080-exec-5] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: ************ - 检查项数量: 1
2025-08-21 11:12:47.542 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 11:12:47.549 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication v1.0-SNAPSHOT using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 296 (D:\git20250402\aegis\target\aegis-1.0-SNAPSHOT.jar started by Administrator in D:\git20250402\aegis)
2025-08-21 11:12:47.550 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 11:12:48.216 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 11:12:48.716 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 11:12:48.722 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 11:12:48.722 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 11:12:48.857 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 11:12:49.122 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 11:12:49.134 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 11:12:49.406 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 11:12:49.443 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.481 seconds (JVM running for 3.037)
2025-08-21 11:13:31.373 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 11:17:12.812 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 11:17:12.828 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 12508 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-21 11:17:12.829 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 11:17:13.705 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 11:17:14.241 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 11:17:14.258 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 11:17:14.258 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 11:17:14.591 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 11:17:14.807 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 11:17:14.820 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 11:17:15.344 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 11:17:15.380 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 3.197 seconds (JVM running for 3.685)
2025-08-21 11:17:21.747 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 11:21:44.438 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 11:21:44.454 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication v1.0-SNAPSHOT using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 21676 (D:\git20250402\aegis\target\aegis-1.0-SNAPSHOT.jar started by Administrator in D:\git20250402\aegis)
2025-08-21 11:21:44.455 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 11:21:45.117 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 11:21:45.591 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 11:21:45.594 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 11:21:45.595 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 11:21:45.707 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 11:21:45.955 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 11:21:45.967 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 11:21:46.249 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 11:21:46.257 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8080 is already in use
2025-08-21 11:21:46.261 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Pausing ProtocolHandler ["http-nio-8080"]
2025-08-21 11:21:46.262 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2025-08-21 11:21:46.268 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Stopping ProtocolHandler ["http-nio-8080"]
2025-08-21 11:21:46.269 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Destroying ProtocolHandler ["http-nio-8080"]
2025-08-21 11:21:46.295 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-08-21 11:22:08.629 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 11:22:08.644 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication v1.0-SNAPSHOT using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 25136 (D:\git20250402\aegis\target\aegis-1.0-SNAPSHOT.jar started by Administrator in D:\git20250402\aegis)
2025-08-21 11:22:08.645 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 11:22:09.482 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 11:22:10.043 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-21 11:22:10.046 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 11:22:10.046 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 11:22:10.151 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 11:22:10.466 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 11:22:10.501 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 11:22:10.755 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-21 11:22:10.785 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.658 seconds (JVM running for 3.059)
2025-08-21 11:22:24.880 [http-nio-8081-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 11:43:40.694 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 11:43:40.706 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication v1.0-SNAPSHOT using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 33480 (D:\git20250402\aegis\target\aegis-1.0-SNAPSHOT.jar started by Administrator in D:\git20250402\aegis)
2025-08-21 11:43:40.707 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 11:43:41.383 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 11:43:41.885 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-21 11:43:41.889 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 11:43:41.889 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 11:43:41.999 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 11:43:42.262 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 11:43:42.276 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 11:43:42.551 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-21 11:43:42.579 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.374 seconds (JVM running for 2.776)
2025-08-21 11:44:05.560 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 11:46:46.198 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 11:46:46.208 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 8376 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-21 11:46:46.209 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 11:46:47.031 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 11:46:47.690 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 11:46:47.693 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 11:46:47.693 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 11:46:47.889 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 11:46:48.081 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 11:46:48.094 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 11:46:48.369 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 11:46:48.398 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.784 seconds (JVM running for 3.334)
2025-08-21 11:46:58.392 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 13:33:29.429 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 13:33:29.442 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 8496 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-21 13:33:29.443 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 13:33:30.265 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 13:33:30.870 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 13:33:30.874 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 13:33:30.874 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 13:33:31.092 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 13:33:31.280 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 13:33:31.292 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 13:33:31.567 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 13:33:31.596 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.859 seconds (JVM running for 3.343)
2025-08-21 13:35:19.046 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 13:35:22.446 [http-nio-8080-exec-6] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-21 13:35:22.446 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-21 13:35:23.064 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-21 13:35:31.992 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: ************ - 检查项数量: 1
2025-08-21 13:36:47.077 [http-nio-8080-exec-7] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: ************ - 检查项数量: 1
2025-08-21 13:36:59.212 [http-nio-8080-exec-8] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: ************ - 检查项数量: 1
2025-08-21 13:36:59.891 [http-nio-8080-exec-5] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: ************ - 检查项数量: 1
2025-08-21 13:36:59.891 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - lastb | head -10 2>/dev/null || echo '无登录失败记录'
2025-08-21 13:37:00.126 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 115
2025-08-21 13:37:32.029 [http-nio-8080-exec-3] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: ************ - 检查项数量: 1
2025-08-21 13:37:32.709 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: ************ - 检查项数量: 1
2025-08-21 13:37:32.710 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - lastb | head -10 2>/dev/null || echo '无登录失败记录'
2025-08-21 13:37:32.878 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 115
2025-08-21 13:37:33.393 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: ************ - 检查项数量: 1
2025-08-21 13:37:33.393 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - ip route show
2025-08-21 13:37:33.548 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 344
2025-08-21 13:37:34.057 [http-nio-8080-exec-6] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: ************ - 检查项数量: 1
2025-08-21 13:37:34.058 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - netstat -tuln
2025-08-21 13:37:34.290 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 5963
2025-08-21 13:43:15.758 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 13:43:15.770 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication v1.0-SNAPSHOT using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 14360 (D:\git20250402\aegis\target\aegis-1.0-SNAPSHOT.jar started by Administrator in D:\git20250402\aegis)
2025-08-21 13:43:15.771 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 13:43:16.439 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 13:43:16.908 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-21 13:43:16.910 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 13:43:16.910 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 13:43:17.024 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 13:43:17.311 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 13:43:17.324 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 13:43:17.621 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-21 13:43:17.650 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.391 seconds (JVM running for 2.865)
2025-08-21 13:43:28.357 [http-nio-8081-exec-3] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 13:43:30.495 [http-nio-8081-exec-1] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-21 13:43:30.496 [http-nio-8081-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-21 13:43:31.152 [http-nio-8081-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-21 13:43:37.509 [http-nio-8081-exec-2] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: ************ - 检查项数量: 1
2025-08-21 14:00:02.461 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 14:00:02.471 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication v1.0-SNAPSHOT using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 8388 (D:\git20250402\aegis\target\aegis-1.0-SNAPSHOT.jar started by Administrator in D:\git20250402\aegis)
2025-08-21 14:00:02.472 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 14:00:03.124 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 14:00:03.644 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-21 14:00:03.647 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 14:00:03.648 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 14:00:03.749 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 14:00:03.988 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 14:00:04.002 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 14:00:04.273 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-21 14:00:04.304 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.339 seconds (JVM running for 2.829)
2025-08-21 14:00:06.756 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 14:00:12.010 [http-nio-8081-exec-3] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-21 14:00:12.013 [http-nio-8081-exec-3] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-21 14:00:12.642 [http-nio-8081-exec-3] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-21 14:00:12.642 [http-nio-8081-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-21 14:00:12.823 [http-nio-8081-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3337
2025-08-21 14:00:12.841 [http-nio-8081-exec-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-21 14:00:12.974 [http-nio-8081-exec-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-21 14:08:54.815 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 14:08:54.826 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 22704 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-21 14:08:54.827 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 14:08:55.544 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 14:08:56.038 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 14:08:56.043 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 14:08:56.043 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 14:08:56.216 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 14:08:56.404 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 14:08:56.417 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 14:08:56.700 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 14:08:56.732 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.538 seconds (JVM running for 3.042)
2025-08-21 14:09:11.571 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 14:09:15.622 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-21 14:09:15.623 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-21 14:09:16.265 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-21 14:09:16.266 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-21 14:09:16.413 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3337
2025-08-21 14:09:16.427 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-21 14:09:16.590 [http-nio-8080-exec-1] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-21 14:15:51.771 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 14:15:51.785 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication v1.0-SNAPSHOT using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 16904 (D:\git20250402\aegis\target\aegis-1.0-SNAPSHOT.jar started by Administrator in D:\git20250402\aegis)
2025-08-21 14:15:51.786 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 14:15:52.439 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 14:15:52.934 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8081"]
2025-08-21 14:15:52.937 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 14:15:52.938 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 14:15:53.062 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 14:15:53.316 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 14:15:53.331 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 14:15:53.602 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8081"]
2025-08-21 14:15:53.634 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.346 seconds (JVM running for 2.777)
2025-08-21 14:16:04.962 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 14:16:07.289 [http-nio-8081-exec-3] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-21 14:16:07.289 [http-nio-8081-exec-3] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-21 14:16:07.842 [http-nio-8081-exec-3] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-21 14:16:07.842 [http-nio-8081-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-21 14:16:08.008 [http-nio-8081-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3337
2025-08-21 14:16:08.022 [http-nio-8081-exec-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-21 14:16:08.184 [http-nio-8081-exec-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-21 14:16:16.878 [http-nio-8081-exec-7] INFO  c.c.d.c.ServerCheckController - 测试数据库连接: ************:5432
2025-08-21 14:16:16.880 [http-nio-8081-exec-7] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-08-21 14:16:16.882 [http-nio-8081-exec-7] WARN  org.postgresql.Driver - JDBC URL must contain a / at the end of the host or port: ***********************************
2025-08-21 14:16:16.882 [http-nio-8081-exec-7] ERROR c.c.d.service.DatabaseService - 测试数据库连接失败: Driver org.postgresql.Driver claims to not accept jdbcUrl, ***********************************
2025-08-21 14:16:26.644 [http-nio-8081-exec-8] INFO  c.c.d.c.ServerCheckController - 测试数据库连接: ************:5432
2025-08-21 14:16:26.645 [http-nio-8081-exec-8] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Starting...
2025-08-21 14:16:26.646 [http-nio-8081-exec-8] WARN  org.postgresql.Driver - JDBC URL must contain a / at the end of the host or port: ***********************************
2025-08-21 14:16:26.647 [http-nio-8081-exec-8] ERROR c.c.d.service.DatabaseService - 测试数据库连接失败: Driver org.postgresql.Driver claims to not accept jdbcUrl, ***********************************
2025-08-21 14:17:21.555 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 14:17:21.567 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 20176 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-21 14:17:21.567 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 14:17:22.355 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 14:17:22.852 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 14:17:22.855 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 14:17:22.856 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 14:17:23.039 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 14:17:23.356 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 14:17:23.377 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 14:17:23.738 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 14:17:23.831 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.926 seconds (JVM running for 3.482)
2025-08-21 14:17:37.625 [http-nio-8080-exec-4] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 14:18:46.261 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-21 14:18:46.262 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-21 14:18:46.845 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-21 14:18:46.845 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-21 14:18:47.019 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3337
2025-08-21 14:18:47.039 [http-nio-8080-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-21 14:18:47.224 [http-nio-8080-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-21 14:22:10.379 [http-nio-8080-exec-8] INFO  c.c.d.c.ServerCheckController - 测试数据库连接: ************:5432
2025-08-21 14:22:10.379 [http-nio-8080-exec-8] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-2 - Starting...
2025-08-21 14:22:10.380 [http-nio-8080-exec-8] WARN  org.postgresql.Driver - JDBC URL must contain a / at the end of the host or port: ***********************************
2025-08-21 14:22:10.380 [http-nio-8080-exec-8] ERROR c.c.d.service.DatabaseService - 测试数据库连接失败: Driver org.postgresql.Driver claims to not accept jdbcUrl, ***********************************
2025-08-21 14:22:24.447 [http-nio-8080-exec-9] INFO  c.c.d.c.ServerCheckController - 测试数据库连接: ************:5432
2025-08-21 14:22:24.447 [http-nio-8080-exec-9] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-3 - Starting...
2025-08-21 14:22:24.448 [http-nio-8080-exec-9] WARN  org.postgresql.Driver - JDBC URL must contain a / at the end of the host or port: ***********************************
2025-08-21 14:22:24.448 [http-nio-8080-exec-9] ERROR c.c.d.service.DatabaseService - 测试数据库连接失败: Driver org.postgresql.Driver claims to not accept jdbcUrl, ***********************************
2025-08-21 14:26:23.484 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 14:26:23.504 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication v1.0-SNAPSHOT using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 14908 (D:\git20250402\aegis\target\aegis-1.0-SNAPSHOT.jar started by Administrator in D:\git20250402\aegis)
2025-08-21 14:26:23.504 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 14:26:24.155 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 14:26:24.640 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 14:26:24.644 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 14:26:24.644 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 14:26:24.747 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 14:26:24.997 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 14:26:25.012 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 14:26:25.288 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 14:26:25.318 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.339 seconds (JVM running for 2.788)
2025-08-21 14:26:43.724 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 14:26:48.965 [http-nio-8080-exec-3] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-21 14:26:48.966 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-21 14:26:49.588 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-21 14:26:49.589 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-21 14:26:49.838 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3337
2025-08-21 14:26:49.854 [http-nio-8080-exec-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-21 14:26:50.025 [http-nio-8080-exec-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-21 14:26:56.183 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 测试数据库连接: ************:5432
2025-08-21 14:27:57.018 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 14:27:57.032 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 14580 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-21 14:27:57.033 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 14:27:57.806 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 14:27:58.331 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 14:27:58.333 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 14:27:58.334 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 14:27:58.504 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 14:27:58.691 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 14:27:58.706 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 14:27:59.041 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 14:27:59.080 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.733 seconds (JVM running for 3.255)
2025-08-21 14:29:50.951 [http-nio-8080-exec-3] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 14:33:52.945 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 14:33:52.956 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 32440 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-21 14:33:52.957 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 14:33:53.896 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 14:33:54.418 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 14:33:54.421 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 14:33:54.421 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 14:33:54.596 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 14:33:54.801 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 14:33:54.815 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共15个检查项
2025-08-21 14:33:55.088 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 14:33:55.121 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.74 seconds (JVM running for 3.208)
2025-08-21 14:33:57.715 [http-nio-8080-exec-2] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 14:34:03.281 [http-nio-8080-exec-3] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-08-21 14:34:03.281 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-21 14:34:03.944 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-21 14:34:03.945 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-08-21 14:34:04.208 [http-nio-8080-exec-3] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3337
2025-08-21 14:34:04.222 [http-nio-8080-exec-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-21 14:34:04.368 [http-nio-8080-exec-3] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-21 14:34:07.963 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 执行服务器检查: ************ - 检查项数量: 1
2025-08-21 14:34:07.965 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-08-21 14:34:08.078 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-08-21 17:40:42.586 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 17:40:42.599 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication v1.0-SNAPSHOT using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 25948 (D:\git20250402\aegis\target\aegis-1.0-SNAPSHOT.jar started by Administrator in D:\git20250402\aegis)
2025-08-21 17:40:42.600 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 17:40:43.285 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 17:40:43.811 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 17:40:43.815 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 17:40:43.816 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 17:40:44.166 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 17:40:44.541 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 17:40:44.567 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共19个检查项
2025-08-21 17:40:44.877 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 17:40:44.928 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.907 seconds (JVM running for 3.44)
2025-08-21 17:40:47.533 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-21 17:41:56.455 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-08-21 17:41:56.465 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 24616 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-08-21 17:41:56.466 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-08-21 17:41:57.181 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-08-21 17:41:57.689 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-21 17:41:57.692 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-21 17:41:57.692 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-08-21 17:41:57.861 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-21 17:41:58.074 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-08-21 17:41:58.091 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共19个检查项
2025-08-21 17:41:58.384 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-21 17:41:58.416 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.554 seconds (JVM running for 3.047)
2025-08-21 17:42:10.720 [http-nio-8080-exec-5] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
