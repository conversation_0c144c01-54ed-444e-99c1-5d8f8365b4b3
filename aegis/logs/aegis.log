2025-09-01 14:27:18.227 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-09-01 14:27:18.239 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 4288 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-09-01 14:27:18.259 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-01 14:27:19.192 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-09-01 14:27:19.676 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-09-01 14:27:19.678 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-09-01 14:27:19.679 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-01 14:27:19.871 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-09-01 14:27:20.068 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-09-01 14:27:20.085 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共19个检查项
2025-09-01 14:27:20.380 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-09-01 14:27:20.411 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.791 seconds (JVM running for 3.491)
2025-09-01 14:27:24.951 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-01 14:27:36.137 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-09-01 14:27:36.137 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-09-01 14:27:36.713 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-09-01 14:27:36.713 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-09-01 14:27:36.955 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-09-01 14:27:36.983 [http-nio-8080-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-09-01 14:27:37.092 [http-nio-8080-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-09-01 14:27:37.099 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-09-01 14:27:37.314 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
