2025-09-01 14:27:18.227 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-09-01 14:27:18.239 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 4288 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-09-01 14:27:18.259 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-01 14:27:19.192 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-09-01 14:27:19.676 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-09-01 14:27:19.678 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-09-01 14:27:19.679 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-01 14:27:19.871 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-09-01 14:27:20.068 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-09-01 14:27:20.085 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共19个检查项
2025-09-01 14:27:20.380 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-09-01 14:27:20.411 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.791 seconds (JVM running for 3.491)
2025-09-01 14:27:24.951 [http-nio-8080-exec-1] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-01 14:27:36.137 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-09-01 14:27:36.137 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-09-01 14:27:36.713 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-09-01 14:27:36.713 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-09-01 14:27:36.955 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-09-01 14:27:36.983 [http-nio-8080-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-09-01 14:27:37.092 [http-nio-8080-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-09-01 14:27:37.099 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-09-01 14:27:37.314 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-09-01 15:10:36.714 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.5.Final
2025-09-01 15:10:36.725 [main] INFO  com.cet.datacenter.AegisApplication - Starting AegisApplication using Java 1.8.0_221 on DESKTOP-7M8Q4RJ with PID 21056 (D:\git20250402\aegis\target\classes started by Administrator in D:\git20250402\aegis)
2025-09-01 15:10:36.726 [main] INFO  com.cet.datacenter.AegisApplication - No active profile set, falling back to 1 default profile: "default"
2025-09-01 15:10:37.565 [main] ERROR o.a.c.core.AprLifecycleListener - An incompatible version [1.2.12] of the Apache Tomcat Native library is installed, while Tomcat requires version [1.2.14]
2025-09-01 15:10:38.072 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-09-01 15:10:38.076 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-09-01 15:10:38.076 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.83]
2025-09-01 15:10:38.368 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-09-01 15:10:38.544 [main] INFO  c.c.d.service.KnowledgeService - 加载知识库数据成功，共10个知识条目
2025-09-01 15:10:38.562 [main] INFO  c.c.d.service.ServerCheckService - 加载检查项配置成功，共21个检查项
2025-09-01 15:10:38.902 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-09-01 15:10:38.929 [main] INFO  com.cet.datacenter.AegisApplication - Started AegisApplication in 2.777 seconds (JVM running for 3.278)
2025-09-01 15:10:41.541 [http-nio-8080-exec-3] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-01 15:10:46.882 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 测试SSH连接: ************
2025-09-01 15:10:46.883 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-09-01 15:10:47.439 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-09-01 15:10:47.440 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-09-01 15:10:47.568 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-09-01 15:10:47.596 [http-nio-8080-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-09-01 15:10:47.726 [http-nio-8080-exec-2] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-09-01 15:10:47.734 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - cat /etc/CET/docker/.env
2025-09-01 15:10:47.976 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 3325
2025-09-01 15:11:05.308 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-09-01 15:11:05.309 [http-nio-8080-exec-1] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-09-01 15:11:08.656 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-09-01 15:11:08.657 [http-nio-8080-exec-4] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-09-01 15:11:08.657 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-09-01 15:11:08.743 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-09-01 15:11:08.743 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'cet_auth-push'
2025-09-01 15:11:08.975 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 29
2025-09-01 15:11:14.851 [http-nio-8080-exec-6] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-09-01 15:11:14.851 [http-nio-8080-exec-6] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-09-01 15:11:14.852 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-09-01 15:11:14.931 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-09-01 15:11:14.931 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'sms_swtich'
2025-09-01 15:11:15.050 [http-nio-8080-exec-6] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 26
2025-09-01 15:11:19.183 [http-nio-8080-exec-7] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-09-01 15:11:19.184 [http-nio-8080-exec-7] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-09-01 15:11:19.184 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-09-01 15:11:19.269 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-09-01 15:11:19.270 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - ls /etc/CET/only-report/config/ 2>/dev/null || echo 'directory not found'
2025-09-01 15:11:19.408 [http-nio-8080-exec-7] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 197
2025-09-01 15:11:23.055 [http-nio-8080-exec-9] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-09-01 15:11:23.055 [http-nio-8080-exec-9] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-09-01 15:11:23.055 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-09-01 15:11:23.130 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-09-01 15:11:23.131 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep -n $'\t' /etc/CET/docker/docker-compose.yml 2>/dev/null || echo 'no tabs found'
2025-09-01 15:11:23.252 [http-nio-8080-exec-9] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 14
2025-09-01 15:11:27.090 [http-nio-8080-exec-8] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-09-01 15:11:27.090 [http-nio-8080-exec-8] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-09-01 15:11:27.091 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-09-01 15:11:27.166 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-09-01 15:11:27.167 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep 'WspCfgShareSize=' /etc/CET/Common/Share.ini | cut -d'=' -f2
2025-09-01 15:11:27.294 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 8
2025-09-01 15:11:27.294 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-09-01 15:11:27.372 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-09-01 15:11:27.372 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep 'WspCfgShareSize=' /etc/CET/Common/Share.ini | cut -d'=' -f2
2025-09-01 15:11:27.514 [http-nio-8080-exec-8] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 8
2025-09-01 15:11:33.147 [http-nio-8080-exec-5] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-09-01 15:11:33.147 [http-nio-8080-exec-5] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-09-01 15:11:33.148 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-09-01 15:11:33.234 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-09-01 15:11:33.235 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep '\[Clear\]' /etc/CET/Common/FrontSC.ini 2>/dev/null || echo 'not found'
2025-09-01 15:11:33.493 [http-nio-8080-exec-5] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 9
2025-09-01 15:12:46.623 [http-nio-8080-exec-2] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-09-01 15:12:46.623 [http-nio-8080-exec-2] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-09-01 15:12:46.623 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-09-01 15:12:46.693 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-09-01 15:12:46.693 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - grep '\[Clear\]' /etc/CET/Common/FrontSC.ini 2>/dev/null || echo 'not found'
2025-09-01 15:12:46.837 [http-nio-8080-exec-2] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 9
2025-09-01 15:13:37.989 [http-nio-8080-exec-1] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-09-01 15:13:37.990 [http-nio-8080-exec-1] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-09-01 15:13:37.990 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-09-01 15:13:38.079 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-09-01 15:13:38.079 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format '{{.Names}}' | head -10
2025-09-01 15:13:42.389 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 228
2025-09-01 15:13:42.390 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-data-center-service-epms-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10
2025-09-01 15:13:42.781 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 2156
2025-09-01 15:13:42.784 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-dcim-web-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10
2025-09-01 15:13:43.031 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-09-01 15:13:43.031 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-cloud-auth-service-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10
2025-09-01 15:13:43.311 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-09-01 15:13:43.311 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 flink-dc 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10
2025-09-01 15:13:43.545 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 1119
2025-09-01 15:13:43.547 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 docker-acmai-service-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10
2025-09-01 15:13:43.823 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-09-01 15:13:43.823 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 common-pechealth-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10
2025-09-01 15:13:44.168 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 1492
2025-09-01 15:13:44.170 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 common-configserver-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10
2025-09-01 15:13:44.467 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 890
2025-09-01 15:13:44.468 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 common-datacacheupload-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10
2025-09-01 15:13:44.746 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 920
2025-09-01 15:13:44.748 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 common-datacachelibserver-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10
2025-09-01 15:13:45.013 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 0
2025-09-01 15:13:45.014 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker logs --tail 500 common-intellialarm-1 2>&1 | grep -i -E '(exception|error|fatal)' | tail -10
2025-09-01 15:13:45.245 [http-nio-8080-exec-1] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 890
2025-09-01 15:14:22.788 [http-nio-8080-exec-4] INFO  c.c.d.c.ServerCheckController - 执行检查请求: CheckExecutionRequest{vipServer=************, mainServer=************, backupServer=************, collectMainServer=************, collectBackupServer=************, databaseConfig=************, checkItems=1 items}
2025-09-01 15:14:22.789 [http-nio-8080-exec-4] INFO  c.c.d.service.ServerCheckService - 开始执行检查，检查项数量: 1
2025-09-01 15:14:22.789 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 开始测试SSH连接: ************
2025-09-01 15:14:22.927 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH连接测试结果: ************ - 成功
2025-09-01 15:14:22.927 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep netclient
2025-09-01 15:14:23.167 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 50
2025-09-01 15:14:23.168 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - 执行SSH命令: ************ - docker ps --format 'table {{.Image}}' | grep netclient
2025-09-01 15:14:23.432 [http-nio-8080-exec-4] INFO  c.cet.datacenter.service.SshService - SSH命令执行成功: ************ - 输出长度: 50
