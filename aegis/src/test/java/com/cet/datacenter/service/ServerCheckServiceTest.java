package com.cet.datacenter.service;

import com.cet.datacenter.entity.CheckExecutionRequest;
import com.cet.datacenter.entity.CheckResult;
import com.cet.datacenter.entity.ServerInfo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.io.ResourceLoader;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ServerCheckService测试类
 * 验证重构后的检查框架是否正常工作
 */
public class ServerCheckServiceTest {

    @Mock
    private SshService sshService;

    @Mock
    private DatabaseService databaseService;

    @Mock
    private ResourceLoader resourceLoader;

    @InjectMocks
    private ServerCheckService serverCheckService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testAlarmPointLimitCheck() {
        // 准备测试数据
        CheckExecutionRequest request = new CheckExecutionRequest();
        request.setAlarmPointCount("500000");
        
        ServerInfo collectMainServer = new ServerInfo();
        collectMainServer.setHost("*************");
        collectMainServer.setPort(22);
        collectMainServer.setUsername("test");
        collectMainServer.setPassword("test");
        request.setCollectMainServer(collectMainServer);

        // Mock SSH连接和命令执行
        when(sshService.testConnection(any(ServerInfo.class))).thenReturn(true);
        when(sshService.executeCommand(any(ServerInfo.class), anyString())).thenReturn("1000000");

        // 执行检查
        List<CheckResult> results = serverCheckService.executeCheckItems(request);

        // 验证结果
        assertNotNull(results);
        // 由于我们没有加载实际的检查项配置，这里主要验证方法不会抛出异常
    }

    @Test
    void testCheckExecutionRequestWithAlarmPointCount() {
        // 测试CheckExecutionRequest的新字段
        CheckExecutionRequest request = new CheckExecutionRequest();
        
        // 测试setter和getter
        request.setAlarmPointCount("123456");
        assertEquals("123456", request.getAlarmPointCount());
        
        // 测试null值
        request.setAlarmPointCount(null);
        assertNull(request.getAlarmPointCount());
        
        // 测试空字符串
        request.setAlarmPointCount("");
        assertEquals("", request.getAlarmPointCount());
    }

    @Test
    void testSimplifyExceptionMessage() {
        // 这个测试需要通过反射来测试私有方法，或者我们可以创建一个包含异常信息的测试
        // 由于simplifyExceptionMessage是私有方法，我们通过集成测试来验证其功能
        assertTrue(true); // 占位测试，实际应该通过集成测试验证
    }
}
