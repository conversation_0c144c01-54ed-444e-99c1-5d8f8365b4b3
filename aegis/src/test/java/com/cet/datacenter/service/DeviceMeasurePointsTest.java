package com.cet.datacenter.service;

import com.cet.datacenter.entity.CheckExecutionRequest;
import com.cet.datacenter.entity.CheckResult;
import com.cet.datacenter.entity.DatabaseConfig;
import com.cet.datacenter.entity.ServerInfo;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.io.ResourceLoader;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 同类设备测点检查测试类
 */
public class DeviceMeasurePointsTest {

    @Mock
    private SshService sshService;

    @Mock
    private DatabaseService databaseService;

    @Mock
    private ResourceLoader resourceLoader;

    @InjectMocks
    private ServerCheckService serverCheckService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testDeviceMeasurePointsCheckWithValidInput() {
        // 准备测试数据
        CheckExecutionRequest request = new CheckExecutionRequest();
        request.setMeterType("电表");
        request.setSubMeterType("智能电表A型");
        
        // 设置数据库配置
        DatabaseConfig dbConfig = new DatabaseConfig();
        dbConfig.setUrl("localhost");
        dbConfig.setPort(5432);
        dbConfig.setUsername("test");
        dbConfig.setPassword("test");
        request.setDatabaseConfig(dbConfig);
        
        // 设置VIP服务器
        ServerInfo vipServer = new ServerInfo();
        vipServer.setHost("*************");
        vipServer.setPort(22);
        request.setVipServer(vipServer);

        // Mock数据库查询结果
        when(databaseService.executeQuerySingleValue(any(DatabaseConfig.class), anyString(), anyString()))
            .thenReturn("1") // metertype value
            .thenReturn("101"); // submetertype id

        // Mock设备查询结果
        Map<String, Object> deviceInfo = new HashMap<>();
        deviceInfo.put("min_deviceid", "1001");
        deviceInfo.put("max_deviceid", "1004");
        deviceInfo.put("min_id_deviceid", "1002");
        deviceInfo.put("max_id_deviceid", "1003");
        
        List<Map<String, Object>> deviceResults = Arrays.asList(deviceInfo);
        when(databaseService.executeQueryForList(any(DatabaseConfig.class), anyString(), any(), any(), any(), any(), any(), any()))
            .thenReturn(deviceResults);

        // 执行检查（这里会因为API调用失败而失败，但我们主要测试参数验证）
        List<CheckResult> results = serverCheckService.executeCheckItems(request);

        // 验证结果
        assertNotNull(results);
        // 由于我们没有加载实际的检查项配置，这里主要验证方法不会抛出异常
    }

    @Test
    void testDeviceMeasurePointsCheckWithEmptyMeterType() {
        // 测试表计类型为空的情况
        CheckExecutionRequest request = new CheckExecutionRequest();
        request.setMeterType(""); // 空的表计类型
        request.setSubMeterType("智能电表A型");
        
        DatabaseConfig dbConfig = new DatabaseConfig();
        dbConfig.setUrl("localhost");
        request.setDatabaseConfig(dbConfig);

        // 执行检查
        List<CheckResult> results = serverCheckService.executeCheckItems(request);

        // 验证结果
        assertNotNull(results);
        // 应该会因为表计类型为空而失败
    }

    @Test
    void testDeviceMeasurePointsCheckWithEmptySubMeterType() {
        // 测试表计型号为空的情况
        CheckExecutionRequest request = new CheckExecutionRequest();
        request.setMeterType("电表");
        request.setSubMeterType(""); // 空的表计型号
        
        DatabaseConfig dbConfig = new DatabaseConfig();
        dbConfig.setUrl("localhost");
        request.setDatabaseConfig(dbConfig);

        // 执行检查
        List<CheckResult> results = serverCheckService.executeCheckItems(request);

        // 验证结果
        assertNotNull(results);
        // 应该会因为表计型号为空而失败
    }

    @Test
    void testCheckExecutionRequestFields() {
        // 测试CheckExecutionRequest的新字段
        CheckExecutionRequest request = new CheckExecutionRequest();
        
        // 测试meterType字段
        request.setMeterType("电表");
        assertEquals("电表", request.getMeterType());
        
        // 测试subMeterType字段
        request.setSubMeterType("智能电表A型");
        assertEquals("智能电表A型", request.getSubMeterType());
        
        // 测试null值
        request.setMeterType(null);
        assertNull(request.getMeterType());
        
        request.setSubMeterType(null);
        assertNull(request.getSubMeterType());
    }
}
