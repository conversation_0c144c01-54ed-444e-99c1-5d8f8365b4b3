/* Aegis服务器管理系统样式文件 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* 主页样式 */
.hero-section {
    padding: 2rem 0;
}

.hero-icon {
    font-size: 4rem;
}

.feature-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.feature-icon {
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 问题球样式 */
.knowledge-container {
    min-height: 600px;
    position: relative;
    overflow: hidden;
}

.knowledge-ball {
    position: absolute;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    padding: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    user-select: none;
}

.knowledge-ball:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(0,0,0,0.3);
    z-index: 10;
}

.knowledge-ball.size-large {
    width: 120px;
    height: 120px;
    font-size: 14px;
}

.knowledge-ball.size-medium {
    width: 100px;
    height: 100px;
    font-size: 12px;
}

.knowledge-ball.size-small {
    width: 80px;
    height: 80px;
    font-size: 11px;
}

/* 搜索框样式 */
.search-container {
    position: relative;
    margin-bottom: 2rem;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    z-index: 1000;
    max-height: 300px;
    overflow-y: auto;
}

.search-result-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-result-item:hover {
    background-color: #f8f9fa;
}

.search-result-item:last-child {
    border-bottom: none;
}

/* 服务器检查样式 */
.server-form {
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.check-items-container {
    max-height: 400px;
    overflow-y: auto;
}

.check-item {
    padding: 0.5rem;
    border: 1px solid #e9ecef;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s;
}

.check-item:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
}

.check-item.selected {
    background-color: #e3f2fd;
    border-color: #2196f3;
}

/* 检查结果样式 */
.check-results {
    margin-top: 2rem;
}

.result-item {
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-radius: 0.375rem;
    border-left: 4px solid;
}

.result-item.success {
    background-color: #d4edda;
    border-left-color: #28a745;
}

.result-item.error {
    background-color: #f8d7da;
    border-left-color: #dc3545;
}

.result-details {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    background-color: #f8f9fa;
    padding: 0.5rem;
    border-radius: 0.25rem;
    margin-top: 0.5rem;
    white-space: pre-wrap;
    word-break: break-all;
}

/* 解决方案详情样式 */
.solution-content {
    background: white;
    padding: 2rem;
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.solution-content h1,
.solution-content h2,
.solution-content h3,
.solution-content h4,
.solution-content h5,
.solution-content h6 {
    color: #2c3e50;
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

.solution-content h1 {
    border-bottom: 2px solid #3498db;
    padding-bottom: 0.5rem;
}

.solution-content h2 {
    border-bottom: 1px solid #bdc3c7;
    padding-bottom: 0.3rem;
}

.solution-content pre {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 1rem;
    border-radius: 0.375rem;
    overflow-x: auto;
}

.solution-content code {
    background-color: #ecf0f1;
    color: #e74c3c;
    padding: 0.2rem 0.4rem;
    border-radius: 0.25rem;
    font-size: 0.9em;
}

.solution-content pre code {
    background-color: transparent;
    color: inherit;
    padding: 0;
}

.solution-content table {
    width: 100%;
    margin: 1rem 0;
}

.solution-content table th {
    background-color: #3498db;
    color: white;
}

.solution-content blockquote {
    border-left: 4px solid #3498db;
    padding-left: 1rem;
    margin: 1rem 0;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.success {
    background-color: #28a745;
}

.status-indicator.error {
    background-color: #dc3545;
}

.status-indicator.warning {
    background-color: #ffc107;
}

.status-indicator.info {
    background-color: #17a2b8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hero-icon {
        font-size: 3rem;
    }
    
    .knowledge-ball {
        position: relative !important;
        margin: 0.5rem;
        display: inline-flex;
    }
    
    .knowledge-container {
        text-align: center;
    }
    
    .server-form {
        margin-bottom: 2rem;
    }
}

/* 工具提示样式 */
.tooltip-inner {
    max-width: 300px;
    text-align: left;
}

/* 按钮样式增强 */
.btn {
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 卡片样式增强 */
.card {
    border: none;
    border-radius: 0.5rem;
    overflow: hidden;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* 表单样式增强 */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}
