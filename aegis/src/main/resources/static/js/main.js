// Aegis服务器管理系统主要JavaScript文件

$(document).ready(function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 初始化弹出框
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
});

// 通用工具函数
const AegisUtils = {
    // 显示加载状态
    showLoading: function(element) {
        const originalText = element.html();
        element.data('original-text', originalText);
        element.html('<span class="loading"></span> 加载中...');
        element.prop('disabled', true);
    },
    
    // 隐藏加载状态
    hideLoading: function(element) {
        const originalText = element.data('original-text');
        element.html(originalText);
        element.prop('disabled', false);
    },
    
    // 显示成功消息
    showSuccess: function(message, container = 'body') {
        this.showAlert('success', message, container);
    },
    
    // 显示错误消息
    showError: function(message, container = 'body') {
        this.showAlert('danger', message, container);
    },
    
    // 显示警告消息
    showWarning: function(message, container = 'body') {
        this.showAlert('warning', message, container);
    },
    
    // 显示信息消息
    showInfo: function(message, container = 'body') {
        this.showAlert('info', message, container);
    },
    
    // 显示警告框
    showAlert: function(type, message, container = 'body') {
        const alertId = 'alert-' + Date.now();
        const alertHtml = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                <i class="fas fa-${this.getAlertIcon(type)} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        $(container).prepend(alertHtml);
        
        // 自动消失
        setTimeout(function() {
            $('#' + alertId).alert('close');
        }, 5000);
    },
    
    // 获取警告图标
    getAlertIcon: function(type) {
        const icons = {
            'success': 'check-circle',
            'danger': 'exclamation-triangle',
            'warning': 'exclamation-circle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    },
    
    // 格式化时间
    formatTime: function(date) {
        if (!date) return '';
        if (typeof date === 'string') {
            date = new Date(date);
        }
        return date.toLocaleString('zh-CN');
    },
    
    // 格式化文件大小
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // 节流函数
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },
    
    // AJAX请求封装
    ajax: function(options) {
        const defaults = {
            type: 'GET',
            dataType: 'json',
            timeout: 30000,
            error: function(xhr, status, error) {
                console.error('AJAX请求失败:', error);
                AegisUtils.showError('请求失败: ' + error);
            }
        };
        
        return $.ajax($.extend(defaults, options));
    },
    
    // 复制到剪贴板
    copyToClipboard: function(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(function() {
                AegisUtils.showSuccess('已复制到剪贴板');
            }).catch(function(err) {
                console.error('复制失败:', err);
                AegisUtils.showError('复制失败');
            });
        } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                AegisUtils.showSuccess('已复制到剪贴板');
            } catch (err) {
                console.error('复制失败:', err);
                AegisUtils.showError('复制失败');
            }
            document.body.removeChild(textArea);
        }
    },
    
    // 验证表单
    validateForm: function(formSelector) {
        const form = $(formSelector);
        let isValid = true;
        
        form.find('input[required], select[required], textarea[required]').each(function() {
            const field = $(this);
            const value = field.val().trim();
            
            if (!value) {
                field.addClass('is-invalid');
                isValid = false;
            } else {
                field.removeClass('is-invalid');
            }
        });
        
        return isValid;
    },
    
    // 清除表单验证状态
    clearFormValidation: function(formSelector) {
        $(formSelector).find('.is-invalid, .is-valid').removeClass('is-invalid is-valid');
    }
};

// 全局错误处理
$(document).ajaxError(function(event, xhr, settings, thrownError) {
    console.error('AJAX全局错误:', {
        url: settings.url,
        status: xhr.status,
        error: thrownError
    });
    
    if (xhr.status === 0) {
        AegisUtils.showError('网络连接失败，请检查网络连接');
    } else if (xhr.status === 404) {
        AegisUtils.showError('请求的资源不存在');
    } else if (xhr.status === 500) {
        AegisUtils.showError('服务器内部错误');
    } else {
        AegisUtils.showError('请求失败: ' + thrownError);
    }
});

// 页面加载完成后的初始化
$(window).on('load', function() {
    // 隐藏加载动画
    $('.page-loading').fadeOut();
    
    // 初始化页面特定功能
    if (typeof initPage === 'function') {
        initPage();
    }
});

// 导出到全局
window.AegisUtils = AegisUtils;
