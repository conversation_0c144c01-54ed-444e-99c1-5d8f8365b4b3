<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title} + ' - Aegis服务器管理系统'">服务器检查 - Aegis服务器管理系统</title>
    
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        
        .navbar {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
            color: #667eea !important;
        }
        
        .nav-link {
            color: #333 !important;
            font-weight: 500;
        }
        
        .nav-link:hover, .nav-link.active {
            color: #667eea !important;
        }
        
        .container {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            margin-top: 2rem;
            margin-bottom: 2rem;
            padding: 2rem;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .page-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .page-title {
            color: #333;
            font-weight: 300;
            margin-bottom: 0.5rem;
        }
        
        .page-subtitle {
            color: #666;
            font-size: 1.1rem;
        }
        
        .config-section {
            margin-bottom: 2rem;
        }
        
        .server-config-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 1rem 1.5rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e0e0e0;
            padding: 0.75rem 1rem;
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 15px rgba(102, 126, 234, 0.2);
        }
        
        .btn {
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            background: transparent;
        }
        
        .btn-outline-primary:hover {
            background: #667eea;
            transform: translateY(-2px);
        }
        
        .checks-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .check-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .check-items-grid {
            display: grid;
            gap: 1.5rem;
        }
        
        .category-section {
            background: #f8f9ff;
            border-radius: 15px;
            padding: 1.5rem;
        }
        
        .category-title {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .check-items-list {
            display: grid;
            gap: 1rem;
        }
        
        .check-item-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: 0 3px 15px rgba(0,0,0,0.08);
            transition: all 0.3s;
            border: 2px solid transparent;
        }
        
        .check-item-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.12);
        }
        
        .check-item-card.selected {
            border-color: #667eea;
            background: linear-gradient(45deg, #f8f9ff, #fff);
        }
        
        .check-item-card.checking {
            border-color: #ffc107;
            background: linear-gradient(45deg, #fff8e1, #fff);
        }
        
        .check-item-card.success {
            border-color: #28a745;
            background: linear-gradient(45deg, #f0fff4, #fff);
        }
        
        .check-item-card.error {
            border-color: #dc3545;
            background: linear-gradient(45deg, #fff5f5, #fff);
        }
        
        .check-item-header {
            display: flex;
            justify-content: between;
            align-items: flex-start;
            margin-bottom: 1rem;
        }
        
        .check-item-info {
            flex: 1;
        }
        
        .check-item-name {
            color: #333;
            font-size: 1.1rem;
            display: block;
            margin-bottom: 0.5rem;
        }
        
        .check-item-desc {
            color: #666;
            line-height: 1.4;
        }
        
        .check-item-status {
            margin-left: 1rem;
        }
        
        .status-badge {
            padding: 0.4rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }
        
        .status-pending {
            background: #e9ecef;
            color: #6c757d;
        }
        
        .status-checking {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-success {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .check-process-details {
            border-top: 1px solid #f0f0f0;
            padding-top: 1rem;
            margin-top: 1rem;
        }
        
        .process-timeline {
            position: relative;
        }
        
        .timeline-item {
            display: flex;
            margin-bottom: 1rem;
            position: relative;
        }
        
        .timeline-item:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 12px;
            top: 30px;
            bottom: -16px;
            width: 2px;
            background: #e0e0e0;
        }
        
        .timeline-marker {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #e0e0e0;
            margin-right: 1rem;
            flex-shrink: 0;
            position: relative;
            z-index: 1;
        }
        
        .timeline-marker.active {
            background: #ffc107;
        }
        
        .timeline-marker.success {
            background: #28a745;
        }
        
        .timeline-marker.error {
            background: #dc3545;
        }
        
        .timeline-content {
            flex: 1;
        }
        
        .process-step {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }
        
        .step-text {
            flex: 1;
            color: #333;
            font-weight: 500;
        }
        
        .step-status {
            font-size: 0.85rem;
            color: #666;
        }
        
        .command-info {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin-top: 0.5rem;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9rem;
        }
        
        .result-output {
            margin-top: 0.5rem;
        }
        
        .output-content {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        .toggle-details {
            background: none;
            border: none;
            color: #667eea;
            font-size: 0.9rem;
            cursor: pointer;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            transition: all 0.3s;
        }
        
        .toggle-details:hover {
            background: #f0f2ff;
        }
        
        .results-summary {
            margin-top: 2rem;
        }
        
        .summary-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .stat-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-align: center;
            padding: 1.5rem;
            border-radius: 15px;
        }
        
        .stat-card h4 {
            margin: 0;
            font-size: 2rem;
        }
        
        .stat-card small {
            opacity: 0.9;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: linear-gradient(45deg, #d4edda, #f8fff9);
            color: #155724;
        }
        
        .alert-danger {
            background: linear-gradient(45deg, #f8d7da, #fff8f8);
            color: #721c24;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 1rem;
                padding: 1rem;
            }
            
            .check-controls {
                flex-direction: column;
                align-items: stretch;
                gap: 0.5rem;
            }
            
            .check-item-header {
                flex-direction: column;
                align-items: stretch;
            }
            
            .check-item-status {
                margin-left: 0;
                margin-top: 0.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" th:href="@{/}">
                <i class="fas fa-shield-alt me-2"></i>
                Aegis
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" th:href="@{/server-check}">
                            <i class="fas fa-server me-1"></i>服务器检查
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/knowledge}">
                            <i class="fas fa-lightbulb me-1"></i>问题球
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <main class="container py-4">
        <!-- 页面标题 -->
        <div class="page-header">
            <h2 class="page-title">
                <i class="fas fa-server me-2"></i>
                服务器检查
            </h2>
            <p class="page-subtitle">连接远程服务器并执行系统检查</p>
        </div>

        <!-- 服务器配置区域 -->
        <div class="config-section">
            <div class="card server-config-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        服务器配置
                    </h5>
                </div>
                <div class="card-body">
                    <form id="serverForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="serverName" class="form-label">
                                        <i class="fas fa-tag me-1"></i>服务器名称
                                    </label>
                                    <input type="text" class="form-control" id="serverName" name="name"
                                           placeholder="例如：生产服务器" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="serverHost" class="form-label">
                                        <i class="fas fa-globe me-1"></i>服务器地址
                                    </label>
                                    <input type="text" class="form-control" id="serverHost" name="host"
                                           placeholder="例如：*************" required>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="serverPort" class="form-label">
                                        <i class="fas fa-plug me-1"></i>端口
                                    </label>
                                    <input type="number" class="form-control" id="serverPort" name="port" value="22" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="serverUsername" class="form-label">
                                        <i class="fas fa-user me-1"></i>用户名
                                    </label>
                                    <input type="text" class="form-control" id="serverUsername" name="username"
                                           placeholder="例如：root" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="serverPassword" class="form-label">
                                        <i class="fas fa-key me-1"></i>密码
                                    </label>
                                    <input type="password" class="form-control" id="serverPassword" name="password"
                                           placeholder="留空使用密钥认证">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="privateKeyPath" class="form-label">
                                        <i class="fas fa-file-key me-1"></i>私钥路径
                                    </label>
                                    <input type="text" class="form-control" id="privateKeyPath" name="privateKeyPath"
                                           placeholder="可选">
                                </div>
                            </div>
                        </div>

                        <div class="action-buttons text-center">
                            <button type="button" class="btn btn-outline-primary me-2" id="testConnectionBtn">
                                <i class="fas fa-plug me-2"></i>测试连接
                            </button>
                            <button type="button" class="btn btn-primary" id="executeChecksBtn" disabled>
                                <i class="fas fa-play me-2"></i>执行检查
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 检查项区域 -->
        <div class="checks-section">
            <div class="card checks-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-list-check me-2"></i>
                            检查项配置
                        </h5>
                        <div class="check-controls">
                            <button type="button" class="btn btn-sm btn-outline-primary me-2" id="selectAllBtn">
                                <i class="fas fa-check-square me-1"></i>全选
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary me-2" id="clearAllBtn">
                                <i class="fas fa-square me-1"></i>清空
                            </button>
                            <select class="form-select form-select-sm" id="categoryFilter" style="width: auto;">
                                <option value="">所有分类</option>
                                <option th:each="category : ${itemsByCategory.keySet()}"
                                        th:value="${category}" th:text="${category}"></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="check-items-grid">
                        <div th:each="category : ${itemsByCategory.keySet()}" class="category-section">
                            <h6 class="category-title" th:text="${category}"></h6>
                            <div class="check-items-list">
                                <div th:each="item : ${itemsByCategory.get(category)}"
                                     class="check-item-card"
                                     th:data-item-id="${item.id}">
                                    <div class="check-item-header">
                                        <div class="form-check">
                                            <input class="form-check-input check-item-input" type="checkbox"
                                                   th:id="'check-' + ${item.id}" th:value="${item.id}">
                                            <label class="form-check-label" th:for="'check-' + ${item.id}">
                                                <div class="check-item-info">
                                                    <strong class="check-item-name" th:text="${item.name}"></strong>
                                                    <small class="check-item-desc" th:text="${item.description}"></small>
                                                </div>
                                            </label>
                                        </div>
                                        <div class="check-item-status">
                                            <span class="status-badge status-pending">待检查</span>
                                            <button class="toggle-details" style="display: none;">
                                                <i class="fas fa-chevron-down me-1"></i>详情
                                            </button>
                                        </div>
                                    </div>

                                    <!-- 检查过程详情（初始隐藏） -->
                                    <div class="check-process-details" style="display: none;">
                                        <div class="process-timeline">
                                            <div class="timeline-item">
                                                <div class="timeline-marker"></div>
                                                <div class="timeline-content">
                                                    <div class="process-step">
                                                        <i class="fas fa-plug me-2"></i>
                                                        <span class="step-text">连接服务器...</span>
                                                        <span class="step-status"></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="timeline-item">
                                                <div class="timeline-marker"></div>
                                                <div class="timeline-content">
                                                    <div class="process-step">
                                                        <i class="fas fa-terminal me-2"></i>
                                                        <span class="step-text">执行命令...</span>
                                                        <span class="step-status"></span>
                                                    </div>
                                                    <div class="command-info">
                                                        <code class="command-text" th:text="${item.command}"></code>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="timeline-item">
                                                <div class="timeline-marker"></div>
                                                <div class="timeline-content">
                                                    <div class="process-step">
                                                        <i class="fas fa-check-circle me-2"></i>
                                                        <span class="step-text">分析结果...</span>
                                                        <span class="step-status"></span>
                                                    </div>
                                                    <div class="result-output">
                                                        <div class="output-content"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 总体结果摘要 -->
        <div id="overallResults" class="results-summary" style="display: none;">
            <div class="card summary-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        检查结果摘要
                    </h5>
                </div>
                <div class="card-body">
                    <div id="resultSummary" class="summary-stats"></div>
                </div>
            </div>
        </div>
    </main>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>

    <script>
        $(document).ready(function() {
            let connectionTested = false;
            let checkResults = {};

            // 工具函数
            function showAlert(type, message, container = '.server-config-card .card-body') {
                $('.alert').remove();

                const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';

                const alertHtml = `
                    <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                        <i class="fas fa-${icon} me-2"></i>
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" onclick="$(this).parent().remove()"></button>
                    </div>
                `;

                $(container).first().prepend(alertHtml);

                setTimeout(function() {
                    $('.alert').fadeOut();
                }, 5000);
            }

            function showLoading(button) {
                const originalText = button.html();
                button.data('original-text', originalText);
                button.html('<span class="loading"></span> 处理中...');
                button.prop('disabled', true);
            }

            function hideLoading(button) {
                const originalText = button.data('original-text');
                button.html(originalText);
                button.prop('disabled', false);
            }

            // 更新检查项状态
            function updateCheckItemStatus(itemId, status, details = null) {
                const card = $(`.check-item-card[data-item-id="${itemId}"]`);
                const statusBadge = card.find('.status-badge');
                const toggleBtn = card.find('.toggle-details');

                // 移除所有状态类
                card.removeClass('selected checking success error');
                statusBadge.removeClass('status-pending status-checking status-success status-error');

                switch(status) {
                    case 'checking':
                        card.addClass('checking');
                        statusBadge.addClass('status-checking').text('检查中...');
                        toggleBtn.show();
                        break;
                    case 'success':
                        card.addClass('success');
                        statusBadge.addClass('status-success').text('成功');
                        toggleBtn.show();
                        break;
                    case 'error':
                        card.addClass('error');
                        statusBadge.addClass('status-error').text('失败');
                        toggleBtn.show();
                        break;
                    default:
                        statusBadge.addClass('status-pending').text('待检查');
                        toggleBtn.hide();
                }

                if (details) {
                    updateCheckItemDetails(itemId, details);
                }
            }

            // 更新检查项详情
            function updateCheckItemDetails(itemId, details) {
                const card = $(`.check-item-card[data-item-id="${itemId}"]`);
                const timeline = card.find('.process-timeline');

                // 更新连接步骤
                const connectStep = timeline.find('.timeline-item:eq(0)');
                const connectMarker = connectStep.find('.timeline-marker');
                const connectStatus = connectStep.find('.step-status');

                if (details.connected) {
                    connectMarker.addClass('success');
                    connectStatus.text('已连接');
                } else if (details.connecting) {
                    connectMarker.addClass('active');
                    connectStatus.text('连接中...');
                } else if (details.connectError) {
                    connectMarker.addClass('error');
                    connectStatus.text('连接失败');
                }

                // 更新执行步骤
                const executeStep = timeline.find('.timeline-item:eq(1)');
                const executeMarker = executeStep.find('.timeline-marker');
                const executeStatus = executeStep.find('.step-status');

                if (details.executed) {
                    executeMarker.addClass('success');
                    executeStatus.text('已执行');
                } else if (details.executing) {
                    executeMarker.addClass('active');
                    executeStatus.text('执行中...');
                } else if (details.executeError) {
                    executeMarker.addClass('error');
                    executeStatus.text('执行失败');
                }

                // 更新结果步骤
                const resultStep = timeline.find('.timeline-item:eq(2)');
                const resultMarker = resultStep.find('.timeline-marker');
                const resultStatus = resultStep.find('.step-status');
                const outputContent = resultStep.find('.output-content');

                if (details.success) {
                    resultMarker.addClass('success');
                    resultStatus.text('检查通过');
                } else if (details.analyzing) {
                    resultMarker.addClass('active');
                    resultStatus.text('分析中...');
                } else if (details.failed) {
                    resultMarker.addClass('error');
                    resultStatus.text('检查失败');
                }

                if (details.output) {
                    outputContent.text(details.output);
                }

                if (details.errorMessage) {
                    outputContent.html(`<span style="color: #dc3545;">${details.errorMessage}</span>`);
                }
            }

            // 测试连接
            $('#testConnectionBtn').click(function() {
                const requiredFields = ['#serverHost', '#serverPort', '#serverUsername'];
                let isValid = true;

                requiredFields.forEach(function(field) {
                    if (!$(field).val().trim()) {
                        $(field).addClass('is-invalid');
                        isValid = false;
                    } else {
                        $(field).removeClass('is-invalid');
                    }
                });

                if (!isValid) {
                    showAlert('error', '请填写必填字段');
                    return;
                }

                const btn = $(this);
                showLoading(btn);

                const serverInfo = {
                    name: $('#serverName').val() || '未命名服务器',
                    host: $('#serverHost').val(),
                    port: parseInt($('#serverPort').val()),
                    username: $('#serverUsername').val(),
                    password: $('#serverPassword').val(),
                    privateKeyPath: $('#privateKeyPath').val()
                };

                $.ajax({
                    url: '/server-check/test-connection',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(serverInfo),
                    success: function(response) {
                        if (response.success) {
                            showAlert('success', response.message);
                            connectionTested = true;
                            $('#executeChecksBtn').prop('disabled', false);
                        } else {
                            showAlert('error', response.message);
                            connectionTested = false;
                            $('#executeChecksBtn').prop('disabled', true);
                        }
                    },
                    error: function() {
                        showAlert('error', '连接测试失败，请检查网络连接');
                        connectionTested = false;
                        $('#executeChecksBtn').prop('disabled', true);
                    },
                    complete: function() {
                        hideLoading(btn);
                    }
                });
            });

            // 执行检查
            $('#executeChecksBtn').click(function() {
                if (!connectionTested) {
                    showAlert('error', '请先测试连接');
                    return;
                }

                const selectedItems = $('.check-item-input:checked').map(function() {
                    return $(this).val();
                }).get();

                if (selectedItems.length === 0) {
                    showAlert('error', '请选择至少一个检查项');
                    return;
                }

                const btn = $(this);
                showLoading(btn);

                // 重置所有检查项状态
                selectedItems.forEach(function(itemId) {
                    updateCheckItemStatus(itemId, 'checking', {
                        connecting: true
                    });
                });

                const serverInfo = {
                    name: $('#serverName').val() || '未命名服务器',
                    host: $('#serverHost').val(),
                    port: parseInt($('#serverPort').val()),
                    username: $('#serverUsername').val(),
                    password: $('#serverPassword').val(),
                    privateKeyPath: $('#privateKeyPath').val()
                };

                const requestData = {
                    serverInfo: serverInfo,
                    checkItems: selectedItems
                };

                // 模拟逐个检查过程
                executeChecksSequentially(requestData, selectedItems, btn);
            });

            // 逐个执行检查
            function executeChecksSequentially(requestData, selectedItems, btn) {
                let currentIndex = 0;

                function executeNext() {
                    if (currentIndex >= selectedItems.length) {
                        // 所有检查完成，显示总结
                        displayOverallSummary();
                        hideLoading(btn);
                        return;
                    }

                    const itemId = selectedItems[currentIndex];

                    // 更新当前项状态
                    updateCheckItemStatus(itemId, 'checking', {
                        connected: true,
                        executing: true
                    });

                    // 执行单个检查
                    $.ajax({
                        url: '/server-check/execute-checks',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            serverInfo: requestData.serverInfo,
                            checkItems: [itemId]
                        }),
                        success: function(response) {
                            if (response.success && response.results.length > 0) {
                                const result = response.results[0];
                                checkResults[itemId] = result;

                                const status = result.success ? 'success' : 'error';
                                updateCheckItemStatus(itemId, status, {
                                    connected: true,
                                    executed: true,
                                    success: result.success,
                                    failed: !result.success,
                                    output: result.actualResult,
                                    errorMessage: result.errorMessage
                                });
                            } else {
                                checkResults[itemId] = {
                                    success: false,
                                    errorMessage: response.message || '检查失败'
                                };

                                updateCheckItemStatus(itemId, 'error', {
                                    connected: true,
                                    executeError: true,
                                    failed: true,
                                    errorMessage: response.message || '检查失败'
                                });
                            }
                        },
                        error: function() {
                            checkResults[itemId] = {
                                success: false,
                                errorMessage: '网络错误或服务器无响应'
                            };

                            updateCheckItemStatus(itemId, 'error', {
                                connectError: true,
                                failed: true,
                                errorMessage: '网络错误或服务器无响应'
                            });
                        },
                        complete: function() {
                            currentIndex++;
                            // 延迟执行下一个，让用户看到过程
                            setTimeout(executeNext, 500);
                        }
                    });
                }

                executeNext();
            }

            // 显示总体摘要
            function displayOverallSummary() {
                const total = Object.keys(checkResults).length;
                const success = Object.values(checkResults).filter(r => r.success).length;
                const fail = total - success;
                const successRate = total > 0 ? Math.round(success / total * 100) : 0;

                const summaryHtml = `
                    <div class="stat-card" style="background: linear-gradient(45deg, #667eea, #764ba2);">
                        <h4>${total}</h4>
                        <small>总计</small>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(45deg, #28a745, #20c997);">
                        <h4>${success}</h4>
                        <small>成功</small>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(45deg, #dc3545, #e74c3c);">
                        <h4>${fail}</h4>
                        <small>失败</small>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(45deg, #17a2b8, #20c997);">
                        <h4>${successRate}%</h4>
                        <small>成功率</small>
                    </div>
                `;

                $('#resultSummary').html(summaryHtml);
                $('#overallResults').show();

                // 滚动到结果区域
                $('html, body').animate({
                    scrollTop: $('#overallResults').offset().top - 100
                }, 500);
            }
