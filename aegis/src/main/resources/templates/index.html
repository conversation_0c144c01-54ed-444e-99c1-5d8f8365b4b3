<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title} + ' - Aegis服务器管理系统'">Aegis服务器管理系统</title>

    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .navbar {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
            color: #667eea !important;
        }

        .nav-link {
            color: #333 !important;
            font-weight: 500;
        }

        .nav-link:hover, .nav-link.active {
            color: #667eea !important;
        }

        .container {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            margin-top: 2rem;
            margin-bottom: 2rem;
            padding: 3rem;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .hero-section {
            text-align: center;
            margin-bottom: 3rem;
        }

        .hero-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
        }

        .display-4 {
            font-weight: 300;
            color: #333;
            margin-bottom: 1rem;
        }

        .lead {
            color: #666;
            font-size: 1.2rem;
        }

        .feature-card {
            border: none;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            overflow: hidden;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .feature-card .card-body {
            padding: 2.5rem;
        }

        .feature-icon {
            margin-bottom: 1.5rem;
        }

        .feature-icon i {
            font-size: 3.5rem;
        }

        .card-title {
            color: #333;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .card-text {
            color: #666;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .btn {
            border-radius: 25px;
            padding: 0.75rem 2rem;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-warning {
            background: linear-gradient(45deg, #ffc107, #ff8c00);
            color: white;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
            color: white;
        }

        .features-section {
            margin: 4rem 0;
        }

        .features-section h3 {
            text-align: center;
            color: #333;
            margin-bottom: 3rem;
            font-weight: 300;
        }

        .feature-item {
            text-align: center;
            padding: 2rem 1rem;
        }

        .feature-item i {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .feature-item h5 {
            color: #333;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .feature-item p {
            color: #666;
            line-height: 1.6;
        }

        .quick-start {
            background: linear-gradient(45deg, #f8f9ff, #fff);
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            margin-top: 3rem;
        }

        .quick-start h4 {
            color: #333;
            margin-bottom: 1rem;
        }

        .quick-start p {
            color: #666;
            margin-bottom: 2rem;
        }

        .btn-group .btn {
            margin: 0 0.5rem;
        }

        @media (max-width: 768px) {
            .container {
                margin: 1rem;
                padding: 2rem;
            }

            .hero-icon {
                font-size: 3rem;
            }

            .display-4 {
                font-size: 2rem;
            }

            .btn-group .btn {
                display: block;
                margin: 0.5rem 0;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" th:href="@{/}">
                <i class="fas fa-shield-alt me-2"></i>
                Aegis
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" th:href="@{/}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/server-check}">
                            <i class="fas fa-server me-1"></i>服务器检查
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/knowledge}">
                            <i class="fas fa-lightbulb me-1"></i>问题球
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容 -->
    <main class="container py-5">
        <!-- 欢迎区域 -->
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <div class="hero-section">
                    <i class="fas fa-shield-alt hero-icon text-primary mb-4"></i>
                    <h1 class="display-4 fw-bold text-primary mb-3" th:text="${title}">Aegis服务器管理系统</h1>
                    <p class="lead text-muted mb-5" th:text="${description}">一站式服务器管理和问题解决平台</p>
                </div>
            </div>
        </div>
        
        <!-- 功能卡片 -->
        <div class="row g-4 mt-4">
            <div class="col-md-6">
                <div class="card feature-card h-100 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-server fa-3x text-primary"></i>
                        </div>
                        <h4 class="card-title">服务器检查</h4>
                        <p class="card-text text-muted">
                            通过SSH连接远程服务器，执行系统配置检查，监控服务器运行状态，
                            包括系统负载、内存使用、磁盘空间、网络连接等关键指标。
                        </p>
                        <a th:href="@{/server-check}" class="btn btn-primary">
                            <i class="fas fa-play me-2"></i>开始检查
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card feature-card h-100 shadow-sm">
                    <div class="card-body text-center p-4">
                        <div class="feature-icon mb-3">
                            <i class="fas fa-lightbulb fa-3x text-warning"></i>
                        </div>
                        <h4 class="card-title">问题球</h4>
                        <p class="card-text text-muted">
                            交互式问题解决方案库，包含常见系统问题的详细解决方案，
                            支持关键词搜索，提供Markdown格式的详细说明和操作步骤。
                        </p>
                        <a th:href="@{/knowledge}" class="btn btn-warning">
                            <i class="fas fa-search me-2"></i>探索问题球
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 系统特性 -->
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="text-center mb-4">系统特性</h3>
            </div>
        </div>
        
        <div class="row g-4">
            <div class="col-md-4">
                <div class="text-center">
                    <i class="fas fa-bolt fa-2x text-success mb-3"></i>
                    <h5>快速部署</h5>
                    <p class="text-muted">双击运行，无需复杂配置，内置Web服务器，通过浏览器即可访问。</p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="text-center">
                    <i class="fas fa-file-alt fa-2x text-info mb-3"></i>
                    <h5>详细日志</h5>
                    <p class="text-muted">完整的操作日志记录，支持日志分类和轮转，便于问题追踪和审计。</p>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="text-center">
                    <i class="fas fa-cogs fa-2x text-secondary mb-3"></i>
                    <h5>灵活配置</h5>
                    <p class="text-muted">支持自定义检查项和解决方案，无需数据库，基于文件的配置管理。</p>
                </div>
            </div>
        </div>
        
        <!-- 快速开始 -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card bg-light">
                    <div class="card-body text-center p-4">
                        <h4 class="card-title">快速开始</h4>
                        <p class="card-text">
                            选择一个功能开始使用Aegis服务器管理系统
                        </p>
                        <div class="d-flex justify-content-center gap-3">
                            <a th:href="@{/server-check}" class="btn btn-primary">
                                <i class="fas fa-server me-2"></i>服务器检查
                            </a>
                            <a th:href="@{/knowledge}" class="btn btn-warning">
                                <i class="fas fa-lightbulb me-2"></i>问题球
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- 页脚 -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="text-muted mb-0">
                &copy; 2025 Aegis服务器管理系统 - 
                <small>基于Spring Boot构建</small>
            </p>
        </div>
    </footer>
    
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>

    <script>
        $(document).ready(function() {
            // 添加一些交互效果
            $('.feature-card').hover(
                function() {
                    $(this).find('.feature-icon i').addClass('fa-bounce');
                },
                function() {
                    $(this).find('.feature-icon i').removeClass('fa-bounce');
                }
            );

            // 平滑滚动
            $('a[href^="#"]').on('click', function(event) {
                var target = $(this.getAttribute('href'));
                if( target.length ) {
                    event.preventDefault();
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 100
                    }, 1000);
                }
            });
        });
    </script>
</body>
</html>
