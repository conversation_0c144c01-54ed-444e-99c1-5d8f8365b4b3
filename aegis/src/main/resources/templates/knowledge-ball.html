<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title} + ' - Aegis服务器管理系统'">问题球 - Aegis服务器管理系统</title>
    
    <!-- 本地CSS，避免CDN问题 -->
    <style>
        /* 基础样式 */
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        
        /* 导航栏 */
        .navbar {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
            color: #667eea !important;
        }
        
        .nav-link {
            color: #333 !important;
            font-weight: 500;
            transition: color 0.3s;
        }
        
        .nav-link:hover, .nav-link.active {
            color: #667eea !important;
        }
        
        /* 主要内容 */
        .main-container {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            margin: 2rem;
            padding: 2rem;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .page-title {
            text-align: center;
            color: #333;
            margin-bottom: 2rem;
            font-weight: 300;
        }
        
        .page-title i {
            color: #667eea;
            margin-right: 1rem;
        }
        
        /* 搜索框 */
        .search-container {
            max-width: 600px;
            margin: 0 auto 3rem auto;
            position: relative;
        }
        
        .search-input {
            width: 100%;
            padding: 1rem 1.5rem;
            border: 2px solid #e0e0e0;
            border-radius: 50px;
            font-size: 1.1rem;
            transition: all 0.3s;
            background: white;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 20px rgba(102, 126, 234, 0.2);
        }
        
        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 0.5rem;
        }
        
        .search-result-item {
            padding: 1rem 1.5rem;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .search-result-item:hover {
            background-color: #f8f9ff;
        }
        
        .search-result-item:last-child {
            border-bottom: none;
        }
        
        /* 问题球容器 */
        .knowledge-container {
            min-height: 500px;
            position: relative;
            overflow: hidden;
            border-radius: 15px;
            background: linear-gradient(45deg, #f0f2ff 0%, #fff 100%);
            padding: 2rem;
        }
        
        /* 问题球样式 */
        .knowledge-ball {
            position: absolute;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            padding: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            user-select: none;
            font-size: 13px;
            line-height: 1.2;
        }
        
        .knowledge-ball:hover {
            transform: scale(1.15);
            box-shadow: 0 12px 35px rgba(0,0,0,0.25);
            z-index: 10;
        }
        
        .knowledge-ball.size-large {
            width: 140px;
            height: 140px;
            font-size: 14px;
        }
        
        .knowledge-ball.size-medium {
            width: 120px;
            height: 120px;
            font-size: 13px;
        }
        
        .knowledge-ball.size-small {
            width: 100px;
            height: 100px;
            font-size: 12px;
        }
        
        /* 分类标签 */
        .category-tabs {
            display: flex;
            justify-content: center;
            margin-bottom: 2rem;
            flex-wrap: wrap;
            gap: 0.5rem;
        }
        
        .category-tab {
            padding: 0.5rem 1.5rem;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            background: white;
            color: #666;
            cursor: pointer;
            transition: all 0.3s;
            font-weight: 500;
        }
        
        .category-tab:hover, .category-tab.active {
            border-color: #667eea;
            background: #667eea;
            color: white;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-container {
                margin: 1rem;
                padding: 1rem;
            }
            
            .knowledge-ball {
                position: relative !important;
                margin: 0.5rem;
                display: inline-flex;
            }
            
            .knowledge-container {
                text-align: center;
                min-height: auto;
            }
        }
        
        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 按钮样式 */
        .btn {
            border-radius: 25px;
            padding: 0.5rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" th:href="@{/}">
                <i class="fas fa-shield-alt me-2"></i>
                Aegis
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/server-check}">
                            <i class="fas fa-server me-1"></i>服务器检查
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" th:href="@{/knowledge}">
                            <i class="fas fa-lightbulb me-1"></i>问题球
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容 -->
    <div class="main-container">
        <h2 class="page-title">
            <i class="fas fa-lightbulb"></i>
            问题球 - 知识库
        </h2>
        
        <!-- 搜索框 -->
        <div class="search-container">
            <input type="text" class="search-input" id="searchInput" placeholder="搜索问题关键词...">
            <div id="searchResults" class="search-results" style="display: none;"></div>
        </div>
        
        <!-- 分类标签 -->
        <div class="category-tabs">
            <div class="category-tab active" data-category="">全部</div>
            <div th:each="category : ${itemsByCategory.keySet()}" 
                 class="category-tab" 
                 th:data-category="${category}" 
                 th:text="${category}"></div>
        </div>
        
        <!-- 问题球容器 -->
        <div class="knowledge-container" id="knowledgeContainer">
            <div th:each="item, iterStat : ${knowledgeItems}" 
                 class="knowledge-ball"
                 th:classappend="${item.priority >= 8} ? 'size-large' : (${item.priority >= 5} ? 'size-medium' : 'size-small')"
                 th:style="'background-color: ' + ${item.color} + '; left: ' + ${iterStat.index * 15 + 10} + '%; top: ' + ${iterStat.index * 12 + 10} + '%;'"
                 th:data-id="${item.id}"
                 th:data-category="${item.category}"
                 th:title="${item.description}">
                <span th:text="${item.title}"></span>
            </div>
        </div>
    </div>
    
    <!-- 基础JavaScript库 -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // 随机分布问题球
            distributeKnowledgeBalls();
            
            // 搜索功能
            let searchTimeout;
            $('#searchInput').on('input', function() {
                clearTimeout(searchTimeout);
                const keyword = $(this).val().trim();
                
                if (keyword.length > 0) {
                    searchTimeout = setTimeout(() => {
                        searchKnowledge(keyword);
                    }, 300);
                } else {
                    $('#searchResults').hide();
                }
            });
            
            // 分类筛选
            $('.category-tab').click(function() {
                $('.category-tab').removeClass('active');
                $(this).addClass('active');
                
                const category = $(this).data('category');
                filterByCategory(category);
            });
            
            // 问题球点击事件
            $('.knowledge-ball').click(function() {
                const itemId = $(this).data('id');
                window.open('/knowledge/item/' + itemId, '_blank');
            });
            
            // 点击其他地方隐藏搜索结果
            $(document).click(function(e) {
                if (!$(e.target).closest('.search-container').length) {
                    $('#searchResults').hide();
                }
            });
        });
        
        // 随机分布问题球
        function distributeKnowledgeBalls() {
            const container = $('#knowledgeContainer');
            const containerWidth = container.width();
            const containerHeight = container.height();
            
            $('.knowledge-ball').each(function(index) {
                const ball = $(this);
                const ballWidth = ball.outerWidth();
                const ballHeight = ball.outerHeight();
                
                // 避免重叠的随机位置
                let attempts = 0;
                let left, top;
                
                do {
                    left = Math.random() * (containerWidth - ballWidth);
                    top = Math.random() * (containerHeight - ballHeight);
                    attempts++;
                } while (attempts < 10 && isOverlapping(left, top, ballWidth, ballHeight, index));
                
                ball.css({
                    left: left + 'px',
                    top: top + 'px'
                });
            });
        }
        
        // 检查是否重叠
        function isOverlapping(left, top, width, height, currentIndex) {
            const balls = $('.knowledge-ball');
            for (let i = 0; i < currentIndex; i++) {
                const otherBall = $(balls[i]);
                const otherLeft = parseFloat(otherBall.css('left'));
                const otherTop = parseFloat(otherBall.css('top'));
                const otherWidth = otherBall.outerWidth();
                const otherHeight = otherBall.outerHeight();
                
                if (left < otherLeft + otherWidth + 20 &&
                    left + width + 20 > otherLeft &&
                    top < otherTop + otherHeight + 20 &&
                    top + height + 20 > otherTop) {
                    return true;
                }
            }
            return false;
        }
        
        // 搜索知识库
        function searchKnowledge(keyword) {
            $.ajax({
                url: '/knowledge/search',
                type: 'GET',
                data: { keyword: keyword },
                success: function(results) {
                    displaySearchResults(results);
                },
                error: function() {
                    $('#searchResults').hide();
                }
            });
        }
        
        // 显示搜索结果
        function displaySearchResults(results) {
            const resultsContainer = $('#searchResults');
            resultsContainer.empty();
            
            if (results.length === 0) {
                resultsContainer.html('<div class="search-result-item">未找到相关结果</div>');
            } else {
                results.forEach(function(item) {
                    const resultItem = $(`
                        <div class="search-result-item" data-id="${item.id}">
                            <strong>${item.title}</strong>
                            <br>
                            <small class="text-muted">${item.description}</small>
                        </div>
                    `);
                    
                    resultItem.click(function() {
                        window.open('/knowledge/item/' + item.id, '_blank');
                    });
                    
                    resultsContainer.append(resultItem);
                });
            }
            
            resultsContainer.show();
        }
        
        // 按分类筛选
        function filterByCategory(category) {
            if (category === '') {
                $('.knowledge-ball').show();
            } else {
                $('.knowledge-ball').hide();
                $(`.knowledge-ball[data-category="${category}"]`).show();
            }
        }
        
        // 窗口大小改变时重新分布
        $(window).resize(function() {
            if ($(window).width() > 768) {
                setTimeout(distributeKnowledgeBalls, 100);
            }
        });
    </script>
</body>
</html>
