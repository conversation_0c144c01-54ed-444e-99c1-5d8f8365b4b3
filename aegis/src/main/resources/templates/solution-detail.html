<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title} + ' - Aegis服务器管理系统'">解决方案 - Aegis服务器管理系统</title>
    
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }
        
        .navbar {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
            color: #667eea !important;
        }
        
        .nav-link {
            color: #333 !important;
            font-weight: 500;
        }
        
        .nav-link:hover {
            color: #667eea !important;
        }
        
        .main-container {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            margin: 2rem;
            padding: 2rem;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        
        .solution-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .solution-title {
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .solution-description {
            color: #666;
            font-size: 1.1rem;
        }
        
        .solution-content {
            line-height: 1.8;
            color: #333;
        }
        
        .solution-content h1,
        .solution-content h2,
        .solution-content h3,
        .solution-content h4,
        .solution-content h5,
        .solution-content h6 {
            color: #2c3e50;
            margin-top: 2rem;
            margin-bottom: 1rem;
        }
        
        .solution-content h1 {
            border-bottom: 3px solid #667eea;
            padding-bottom: 0.5rem;
        }
        
        .solution-content h2 {
            border-bottom: 2px solid #e0e0e0;
            padding-bottom: 0.3rem;
        }
        
        .solution-content pre {
            background-color: #2c3e50;
            color: #ecf0f1;
            padding: 1.5rem;
            border-radius: 10px;
            overflow-x: auto;
            margin: 1rem 0;
            position: relative;
        }
        
        .solution-content pre::before {
            content: "代码";
            position: absolute;
            top: 0.5rem;
            right: 1rem;
            color: #95a5a6;
            font-size: 0.8rem;
        }
        
        .solution-content code {
            background-color: #f8f9fa;
            color: #e74c3c;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-size: 0.9em;
        }
        
        .solution-content pre code {
            background-color: transparent;
            color: inherit;
            padding: 0;
        }
        
        .solution-content table {
            width: 100%;
            margin: 1.5rem 0;
            border-collapse: collapse;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .solution-content table th {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 1rem;
            text-align: left;
        }
        
        .solution-content table td {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .solution-content table tr:hover {
            background-color: #f8f9ff;
        }
        
        .solution-content blockquote {
            border-left: 4px solid #667eea;
            padding: 1rem 1.5rem;
            margin: 1.5rem 0;
            background: linear-gradient(90deg, #f8f9ff 0%, #fff 100%);
            border-radius: 0 8px 8px 0;
            position: relative;
        }
        
        .solution-content blockquote::before {
            content: "💡";
            position: absolute;
            left: -2px;
            top: 1rem;
            font-size: 1.2rem;
        }
        
        .solution-content ul, .solution-content ol {
            padding-left: 2rem;
        }
        
        .solution-content li {
            margin-bottom: 0.5rem;
        }
        
        .back-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s;
            margin-bottom: 2rem;
        }
        
        .back-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        @media (max-width: 768px) {
            .main-container {
                margin: 1rem;
                padding: 1rem;
            }
            
            .solution-content pre {
                padding: 1rem;
                font-size: 0.9rem;
            }
            
            .solution-content table {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg">
        <div class="container">
            <a class="navbar-brand" th:href="@{/}">
                <i class="fas fa-shield-alt me-2"></i>
                Aegis
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" th:href="@{/}">
                    <i class="fas fa-home me-1"></i>首页
                </a>
                <a class="nav-link" th:href="@{/server-check}">
                    <i class="fas fa-server me-1"></i>服务器检查
                </a>
                <a class="nav-link" th:href="@{/knowledge}">
                    <i class="fas fa-lightbulb me-1"></i>问题球
                </a>
            </div>
        </div>
    </nav>
    
    <!-- 主要内容 -->
    <div class="main-container">
        <button class="back-button" onclick="history.back()">
            <i class="fas fa-arrow-left me-2"></i>返回
        </button>
        
        <div class="solution-header" th:if="${item}">
            <h1 class="solution-title" th:text="${item.title}">解决方案标题</h1>
            <p class="solution-description" th:text="${item.description}">解决方案描述</p>
        </div>
        
        <div class="solution-content" th:utext="${solutionContent}">
            <!-- 解决方案内容将在这里显示 -->
        </div>
        
        <div th:if="${error}" class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <span th:text="${error}">错误信息</span>
        </div>
    </div>
    
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>
