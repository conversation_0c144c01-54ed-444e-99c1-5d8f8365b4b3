<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title} + ' - Aegis服务器管理系统'">服务器检查 - Aegis服务器管理系统</title>
    
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
        }

        .navbar {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: bold;
            font-size: 1.5rem;
            color: #667eea !important;
        }

        .nav-link {
            color: #333 !important;
            font-weight: 500;
        }

        .nav-link:hover, .nav-link.active {
            color: #667eea !important;
        }

        .container {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            margin-top: 1rem;
            margin-bottom: 1rem;
            padding: 1.5rem;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 0.5rem 1rem;
        }

        .card-header h6 {
            font-size: 0.9rem;
            margin: 0;
        }

        .card-body {
            padding: 1rem;
        }

        .form-control {
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            padding: 0.5rem 0.75rem;
            transition: all 0.3s;
            font-size: 0.9rem;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.15);
        }

        .mb-3 {
            margin-bottom: 0.75rem !important;
        }

        .config-form-inline .form-row {
            display: flex;
            align-items: end;
            gap: 0.75rem;
            flex-wrap: wrap;
        }

        .config-form-inline .form-group {
            display: flex;
            flex-direction: column;
            min-width: 100px;
        }

        .config-form-inline .form-group:nth-child(1) {
            flex: 1.5;
            min-width: 140px;
        }

        .config-form-inline .form-group:nth-child(2) {
            flex: 0 0 70px;
        }

        .config-form-inline .form-group:nth-child(3) {
            flex: 1;
            min-width: 90px;
        }

        .config-form-inline .form-group:nth-child(4) {
            flex: 1;
            min-width: 100px;
        }

        .config-form-inline .form-group:nth-child(5) {
            flex: 1;
            min-width: 90px;
        }

        .config-form-inline .form-actions {
            display: flex;
            gap: 0.5rem;
            align-items: center;
            flex: 0 0 auto;
        }

        .config-form-inline label {
            font-size: 0.8rem;
            margin-bottom: 0.25rem;
            font-weight: 500;
        }

        .config-form-inline .form-control {
            font-size: 0.8rem;
            padding: 0.3rem 0.5rem;
        }

        .mb-2 {
            margin-bottom: 0.5rem !important;
        }

        .btn {
            border-radius: 20px;
            padding: 0.5rem 1.5rem;
            font-weight: 500;
            transition: all 0.3s;
            border: none;
            font-size: 0.9rem;
        }

        .btn-sm {
            padding: 0.375rem 1rem;
            font-size: 0.8rem;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: #667eea;
            transform: translateY(-2px);
        }

        .form-label {
            text-align: left;
            font-weight: 500;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control {
            text-align: left;
        }

        .config-section {
            margin-bottom: 1.5rem;
        }

        .checks-section {
            margin-top: 1rem;
        }

        .check-items-grid {
            display: grid;
            gap: 1rem;
        }

        .category-section {
            background: #f8f9ff;
            border-radius: 12px;
            padding: 1rem;
        }

        .category-title {
            color: #667eea;
            font-weight: 600;
            margin-bottom: 0.5rem;
            padding-bottom: 0.25rem;
            border-bottom: 1px solid #e0e0e0;
            font-size: 0.9rem;
        }

        .check-items-list {
            display: grid;
            gap: 0.75rem;
        }

        .check-item-card {
            background: white;
            border-radius: 8px;
            padding: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.06);
            transition: all 0.3s;
            border: 2px solid transparent;
        }

        .check-item-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.12);
        }

        .check-item-card.selected {
            border-color: #667eea;
            background: linear-gradient(45deg, #f8f9ff, #fff);
        }

        .check-item-card.checking {
            border-color: #ffc107;
            background: linear-gradient(45deg, #fff8e1, #fff);
        }

        .check-item-card.success {
            border-color: #28a745;
            background: linear-gradient(45deg, #f0fff4, #fff);
        }

        .check-item-card.error {
            border-color: #dc3545;
            background: linear-gradient(45deg, #fff5f5, #fff);
        }

        .check-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.75rem;
        }

        .check-item-info {
            flex: 1;
        }

        .check-item-name {
            color: #333;
            font-size: 1rem;
            display: block;
            margin-bottom: 0.25rem;
        }

        .check-item-desc {
            color: #666;
            line-height: 1.3;
            font-size: 0.9rem;
        }

        .check-item-status {
            margin-left: 1rem;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.5rem;
        }

        .check-item-actions {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 0.25rem;
        }

        .single-check-btn {
            font-size: 0.75rem;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            min-width: 60px;
        }

        .single-check-btn:disabled {
            opacity: 0.6;
        }

        .status-badge {
            padding: 0.4rem 1rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .status-pending {
            background: #e9ecef;
            color: #6c757d;
        }

        .status-checking {
            background: #fff3cd;
            color: #856404;
        }

        .status-success {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .toggle-details {
            background: none;
            border: none;
            color: #667eea;
            font-size: 0.9rem;
            cursor: pointer;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            transition: all 0.3s;
        }

        .toggle-details:hover {
            background: #f0f2ff;
        }

        .check-process-details {
            border-top: 1px solid #f0f0f0;
            padding-top: 1rem;
            margin-top: 1rem;
        }

        .process-timeline {
            position: relative;
        }

        .timeline-item {
            display: flex;
            margin-bottom: 1rem;
            position: relative;
        }

        .timeline-item:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 12px;
            top: 30px;
            bottom: -16px;
            width: 2px;
            background: #e0e0e0;
        }

        .timeline-marker {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #e0e0e0;
            margin-right: 1rem;
            flex-shrink: 0;
            position: relative;
            z-index: 1;
        }

        .timeline-marker.active {
            background: #ffc107;
        }

        .timeline-marker.success {
            background: #28a745;
        }

        .timeline-marker.error {
            background: #dc3545;
        }

        .timeline-content {
            flex: 1;
        }

        .process-step {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .step-text {
            flex: 1;
            color: #333;
            font-weight: 500;
        }

        .step-status {
            font-size: 0.85rem;
            color: #666;
        }

        .command-info {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            margin-top: 0.5rem;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9rem;
        }

        .result-output {
            margin-top: 0.5rem;
        }

        .output-content {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }

        .results-summary {
            margin-top: 2rem;
        }

        .summary-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .stat-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-align: center;
            padding: 1.5rem;
            border-radius: 15px;
        }

        .stat-card h4 {
            margin: 0;
            font-size: 2rem;
        }

        .stat-card small {
            opacity: 0.9;
        }

        .result-item {
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-radius: 10px;
            border-left: 5px solid;
        }

        .result-item.success {
            background: linear-gradient(45deg, #d4edda, #f8fff9);
            border-left-color: #28a745;
        }

        .result-item.error {
            background: linear-gradient(45deg, #f8d7da, #fff8f8);
            border-left-color: #dc3545;
        }

        .result-details {
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 0.9rem;
            background: rgba(0,0,0,0.05);
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 200px;
            overflow-y: auto;
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
            margin-bottom: 1rem;
        }

        .alert-success {
            background: linear-gradient(45deg, #d4edda, #f8fff9);
            color: #155724;
        }

        .alert-danger {
            background: linear-gradient(45deg, #f8d7da, #fff8f8);
            color: #721c24;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .summary-card {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            text-align: center;
            padding: 1.5rem;
            border-radius: 15px;
            margin-bottom: 1rem;
        }

        .summary-card h4 {
            margin: 0;
            font-size: 2rem;
        }

        .summary-card small {
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .container {
                margin: 1rem;
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
<!--    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" th:href="@{/}">
                <i class="fas fa-shield-alt me-2"></i>
                Aegis
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/}">
                            <i class="fas fa-home me-1"></i>首页
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" th:href="@{/server-check}">
                            <i class="fas fa-server me-1"></i>服务器检查
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" th:href="@{/knowledge}">
                            <i class="fas fa-lightbulb me-1"></i>问题球
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>-->
    
    <!-- 主要内容 -->
    <main class="container py-3">
        <!-- 服务器与数据库配置区域 -->
        <div class="config-section">
            <div class="card server-config-card">
                <div class="card-header">
                    <h6 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        服务器与数据库配置
                    </h6>
                </div>
                <div class="card-body">
                    <form id="configForm" class="config-form-inline">
                        <!-- 第一行：服务器VIP配置 -->
                        <div class="form-row mb-2">
                            <div class="form-group">
                                <label for="vipHost">服务器VIP地址</label>
                                <input type="text" class="form-control" id="vipHost" name="host"
                                       value="************" required>
                            </div>
                            <div class="form-group">
                                <label for="vipPort">SSH端口</label>
                                <input type="number" class="form-control" id="vipPort" name="port"
                                       value="22" required>
                            </div>
                            <div class="form-group">
                                <label for="vipUsername">SSH用户名</label>
                                <input type="text" class="form-control" id="vipUsername" name="username"
                                       value="root" required>
                            </div>
                            <div class="form-group">
                                <label for="vipPassword">SSH密码</label>
                                <input type="password" class="form-control" id="vipPassword" name="password"
                                       value="ceiec" required>
                            </div>
                            <div class="form-group form-actions">
                                <button type="button" class="btn btn-outline-primary btn-sm" id="testVipConnectionBtn">
                                    <i class="fas fa-plug me-1"></i>测试VIP连接
                                </button>
                            </div>
                        </div>

                        <!-- 第二行：业务主服务器配置 -->
                        <div class="form-row mb-2" id="mainServerRow" style="display: none;">
                            <div class="form-group">
                                <label for="mainHost">业务主服务器地址</label>
                                <input type="text" class="form-control" id="mainHost" name="host"
                                       placeholder="业务主服务器IP">
                            </div>
                            <div class="form-group">
                                <label for="mainPort">SSH端口</label>
                                <input type="number" class="form-control" id="mainPort" name="port"
                                       value="22" readonly>
                            </div>
                            <div class="form-group">
                                <label for="mainUsername">SSH用户名</label>
                                <input type="text" class="form-control" id="mainUsername" name="username"
                                       value="root" readonly>
                            </div>
                            <div class="form-group">
                                <label for="mainPassword">SSH密码</label>
                                <input type="password" class="form-control" id="mainPassword" name="password"
                                       value="ceiec" readonly>
                            </div>
                            <div class="form-group form-actions">
                                <button type="button" class="btn btn-outline-primary btn-sm" id="testMainConnectionBtn">
                                    <i class="fas fa-plug me-1"></i>测试主服务器
                                </button>
                            </div>
                        </div>

                        <!-- 第三行：业务备服务器配置 -->
                        <div class="form-row mb-2" id="backupServerRow" style="display: none;">
                            <div class="form-group">
                                <label for="backupHost">业务备服务器地址</label>
                                <input type="text" class="form-control" id="backupHost" name="host"
                                       placeholder="业务备服务器IP">
                            </div>
                            <div class="form-group">
                                <label for="backupPort">SSH端口</label>
                                <input type="number" class="form-control" id="backupPort" name="port"
                                       value="22" readonly>
                            </div>
                            <div class="form-group">
                                <label for="backupUsername">SSH用户名</label>
                                <input type="text" class="form-control" id="backupUsername" name="username"
                                       value="root" readonly>
                            </div>
                            <div class="form-group">
                                <label for="backupPassword">SSH密码</label>
                                <input type="password" class="form-control" id="backupPassword" name="password"
                                       value="ceiec" readonly>
                            </div>
                            <div class="form-group form-actions">
                                <button type="button" class="btn btn-outline-primary btn-sm" id="testBackupConnectionBtn">
                                    <i class="fas fa-plug me-1"></i>测试备服务器
                                </button>
                            </div>
                        </div>

                        <!-- 第四行：采集主服务器配置 -->
                        <div class="form-row mb-2" id="collectMainServerRow" style="display: none;">
                            <div class="form-group">
                                <label for="collectMainHost">采集主服务器地址</label>
                                <input type="text" class="form-control" id="collectMainHost" name="host"
                                       placeholder="采集主服务器IP">
                            </div>
                            <div class="form-group">
                                <label for="collectMainPort">SSH端口</label>
                                <input type="number" class="form-control" id="collectMainPort" name="port"
                                       value="22" readonly>
                            </div>
                            <div class="form-group">
                                <label for="collectMainUsername">SSH用户名</label>
                                <input type="text" class="form-control" id="collectMainUsername" name="username"
                                       value="root" readonly>
                            </div>
                            <div class="form-group">
                                <label for="collectMainPassword">SSH密码</label>
                                <input type="password" class="form-control" id="collectMainPassword" name="password"
                                       value="ceiec" readonly>
                            </div>
                            <div class="form-group form-actions">
                                <button type="button" class="btn btn-outline-primary btn-sm" id="testCollectMainConnectionBtn">
                                    <i class="fas fa-plug me-1"></i>测试采集主
                                </button>
                            </div>
                        </div>

                        <!-- 第五行：采集备服务器配置 -->
                        <div class="form-row mb-2" id="collectBackupServerRow" style="display: none;">
                            <div class="form-group">
                                <label for="collectBackupHost">采集备服务器地址</label>
                                <input type="text" class="form-control" id="collectBackupHost" name="host"
                                       placeholder="采集备服务器IP">
                            </div>
                            <div class="form-group">
                                <label for="collectBackupPort">SSH端口</label>
                                <input type="number" class="form-control" id="collectBackupPort" name="port"
                                       value="22" readonly>
                            </div>
                            <div class="form-group">
                                <label for="collectBackupUsername">SSH用户名</label>
                                <input type="text" class="form-control" id="collectBackupUsername" name="username"
                                       value="root" readonly>
                            </div>
                            <div class="form-group">
                                <label for="collectBackupPassword">SSH密码</label>
                                <input type="password" class="form-control" id="collectBackupPassword" name="password"
                                       value="ceiec" readonly>
                            </div>
                            <div class="form-group form-actions">
                                <button type="button" class="btn btn-outline-primary btn-sm" id="testCollectBackupConnectionBtn">
                                    <i class="fas fa-plug me-1"></i>测试采集备
                                </button>
                            </div>
                        </div>

                        <!-- 第六行：数据库配置 -->
                        <div class="form-row" id="databaseConfigRow" style="display: none;">
                            <div class="form-group">
                                <label for="dbUrl">数据库地址</label>
                                <input type="text" class="form-control" id="dbUrl" name="url"
                                       placeholder="************">
                            </div>
                            <div class="form-group">
                                <label for="dbPort">DB端口</label>
                                <input type="number" class="form-control" id="dbPort" name="port"
                                       placeholder="5432">
                            </div>
                            <div class="form-group">
                                <label for="dbUsername">DB用户名</label>
                                <input type="text" class="form-control" id="dbUsername" name="username"
                                       placeholder="postgres">
                            </div>
                            <div class="form-group">
                                <label for="dbPassword">DB密码</label>
                                <input type="password" class="form-control" id="dbPassword" name="password"
                                       placeholder="请输入密码">
                            </div>
                            <div class="form-group">
                                <label for="dbProjectName">项目名</label>
                                <input type="text" class="form-control" id="dbProjectName" name="projectName"
                                       placeholder="项目名称">
                            </div>
                            <div class="form-group form-actions">
                                <button type="button" class="btn btn-outline-success btn-sm" id="testDbConnectionBtn">
                                    <i class="fas fa-database me-1"></i>测试数据库
                                </button>
                                <button type="button" class="btn btn-primary btn-sm" id="batchCheckBtn" disabled>
                                    <i class="fas fa-play me-1"></i>批量检查
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 检查项区域 -->
        <div class="checks-section">
            <div class="card checks-card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-list-check me-2"></i>
                            检查项
                        </h6>
                        <div class="check-controls">
                            <button type="button" class="btn btn-sm btn-outline-primary me-2" id="selectAllBtn">
                                <i class="fas fa-check-square me-1"></i>全选
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-secondary me-2" id="clearAllBtn">
                                <i class="fas fa-square me-1"></i>清空
                            </button>
                            <select class="form-select form-select-sm" id="categoryFilter" style="width: auto;">
                                <option value="">所有分类</option>
                                <option th:each="category : ${itemsByCategory.keySet()}"
                                        th:value="${category}" th:text="${category}"></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="check-items-grid">
                        <div th:each="category : ${itemsByCategory.keySet()}" class="category-section">
                            <h6 class="category-title" th:text="${category}"></h6>
                            <div class="check-items-list">
                                <div th:each="item : ${itemsByCategory.get(category)}"
                                     class="check-item-card"
                                     th:data-item-id="${item.id}">
                                    <div class="check-item-header">
                                        <div class="form-check">
                                            <input class="form-check-input check-item-input" type="checkbox"
                                                   th:id="'check-' + ${item.id}" th:value="${item.id}">
                                            <label class="form-check-label" th:for="'check-' + ${item.id}">
                                                <div class="check-item-info">
                                                    <strong class="check-item-name" th:text="${item.name}"></strong>
                                                    <small class="check-item-desc" th:text="${item.description}"></small>
                                                </div>
                                            </label>
                                        </div>
                                        <div class="check-item-status">
                                            <span class="status-badge status-pending">待检查</span>
                                            <div class="check-item-actions">
                                                <button class="btn btn-sm btn-outline-primary single-check-btn"
                                                        th:data-item-id="${item.id}" disabled>
                                                    <i class="fas fa-play me-1"></i>检查
                                                </button>
                                                <button class="toggle-details" style="display: none;">
                                                    <i class="fas fa-chevron-down me-1"></i>详情
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 检查过程详情（初始隐藏） -->
                                    <div class="check-process-details" style="display: none;">
                                        <div class="process-timeline">
                                            <div class="timeline-item">
                                                <div class="timeline-marker"></div>
                                                <div class="timeline-content">
                                                    <div class="process-step">
                                                        <i class="fas fa-plug me-2"></i>
                                                        <span class="step-text">连接服务器...</span>
                                                        <span class="step-status"></span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="timeline-item">
                                                <div class="timeline-marker"></div>
                                                <div class="timeline-content">
                                                    <div class="process-step">
                                                        <i class="fas fa-terminal me-2"></i>
                                                        <span class="step-text">执行命令...</span>
                                                        <span class="step-status"></span>
                                                    </div>
                                                    <div class="command-info">
                                                        <code class="command-text" th:text="${item.command}"></code>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="timeline-item">
                                                <div class="timeline-marker"></div>
                                                <div class="timeline-content">
                                                    <div class="process-step">
                                                        <i class="fas fa-check-circle me-2"></i>
                                                        <span class="step-text">分析结果...</span>
                                                        <span class="step-status"></span>
                                                    </div>
                                                    <div class="result-output">
                                                        <div class="output-content"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 总体结果摘要 -->
        <div id="overallResults" class="results-summary" style="display: none;">
            <div class="card summary-card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>
                        检查结果摘要
                    </h5>
                </div>
                <div class="card-body">
                    <div id="resultSummary" class="summary-stats"></div>
                </div>
            </div>
        </div>
    </main>
    
    <!-- 页脚 -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="text-muted mb-0">
                &copy; 2025 Aegis服务器管理系统 - 
                <small>基于Spring Boot构建</small>
            </p>
        </div>
    </footer>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- 自定义脚本 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>

    <script>
        $(document).ready(function() {
            let connectionTested = false;
            let checkResults = {};

            console.log('页面加载完成，开始绑定事件');
            console.log('测试连接按钮:', $('#testConnectionBtn').length);
            console.log('批量检查按钮:', $('#batchCheckBtn').length);
            console.log('单个检查按钮:', $('.single-check-btn').length);

            // 工具函数
            function showAlert(type, message, container = '.server-config-card .card-body') {
                $('.alert').remove();

                const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
                const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';

                const alertHtml = `
                    <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                        <i class="fas fa-${icon} me-2"></i>
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" onclick="$(this).parent().remove()"></button>
                    </div>
                `;

                $(container).first().prepend(alertHtml);

                setTimeout(function() {
                    $('.alert').fadeOut();
                }, 5000);
            }

            function showLoading(button) {
                const originalText = button.html();
                button.data('original-text', originalText);
                button.html('<span class="loading"></span> 处理中...');
                button.prop('disabled', true);
            }

            function hideLoading(button) {
                const originalText = button.data('original-text');
                button.html(originalText);
                button.prop('disabled', false);
            }

            // 更新检查项状态
            function updateCheckItemStatus(itemId, status, details = null) {
                const card = $(`.check-item-card[data-item-id="${itemId}"]`);
                const statusBadge = card.find('.status-badge');
                const toggleBtn = card.find('.toggle-details');

                // 移除所有状态类
                card.removeClass('selected checking success error');
                statusBadge.removeClass('status-pending status-checking status-success status-error');

                switch(status) {
                    case 'checking':
                        card.addClass('checking');
                        statusBadge.addClass('status-checking').text('检查中...');
                        toggleBtn.show();
                        break;
                    case 'success':
                        card.addClass('success');
                        statusBadge.addClass('status-success').text('成功');
                        toggleBtn.show();
                        break;
                    case 'error':
                        card.addClass('error');
                        statusBadge.addClass('status-error').text('失败');
                        toggleBtn.show();
                        break;
                    default:
                        statusBadge.addClass('status-pending').text('待检查');
                        toggleBtn.hide();
                }

                if (details) {
                    updateCheckItemDetails(itemId, details);
                }
            }

            // 更新检查项详情
            function updateCheckItemDetails(itemId, checkSteps) {
                const card = $(`.check-item-card[data-item-id="${itemId}"]`);
                const timeline = card.find('.process-timeline');

                if (checkSteps && checkSteps.length > 0) {
                    // 清空现有的时间线
                    timeline.empty();

                    // 根据后端返回的步骤重新构建时间线
                    checkSteps.forEach(function(step, index) {
                        const isLast = index === checkSteps.length - 1;
                        const markerClass = step.statusClass || '';
                        const icon = step.stepIcon || 'fas fa-cog';

                        const timelineItem = $(`
                            <div class="timeline-item">
                                <div class="timeline-marker ${markerClass}"></div>
                                <div class="timeline-content">
                                    <div class="process-step">
                                        <i class="${icon} me-2"></i>
                                        <span class="step-text">${step.stepName}</span>
                                        <span class="step-status">${step.status === 'SUCCESS' ? '完成' : (step.status === 'ERROR' ? '失败' : (step.status === 'RUNNING' ? '执行中...' : '待执行'))}</span>
                                    </div>
                                    ${step.command ? `<div class="command-info"><code class="command-text">${step.command}</code></div>` : ''}
                                    ${step.result ? `<div class="result-output"><div class="output-content">${step.result}</div></div>` : ''}
                                    ${step.errorMessage ? `<div class="result-output"><div class="output-content" style="color: #dc3545;">${step.errorMessage}</div></div>` : ''}
                                </div>
                            </div>
                        `);

                        timeline.append(timelineItem);
                    });
                }
            }

            // 测试VIP连接
            $('#testVipConnectionBtn').click(function() {
                console.log('测试VIP连接按钮被点击');
                const requiredFields = ['#vipHost', '#vipPort', '#vipUsername', '#vipPassword'];
                let isValid = true;

                requiredFields.forEach(function(field) {
                    if (!$(field).val().trim()) {
                        $(field).addClass('is-invalid');
                        isValid = false;
                    } else {
                        $(field).removeClass('is-invalid');
                    }
                });

                if (!isValid) {
                    showAlert('error', '请填写所有必填字段');
                    return;
                }

                const btn = $(this);
                showLoading(btn);

                const serverInfo = {
                    name: '服务器VIP',
                    host: $('#vipHost').val(),
                    port: parseInt($('#vipPort').val()),
                    username: $('#vipUsername').val(),
                    password: $('#vipPassword').val(),
                    privateKeyPath: ''
                };

                $.ajax({
                    url: '/server-check/test-connection',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(serverInfo),
                    success: function(response) {
                        if (response.success) {
                            showAlert('success', response.message);
                            connectionTested = true;
                            $('#batchCheckBtn').prop('disabled', false);
                            $('.single-check-btn').prop('disabled', false);

                            // 显示其他配置行
                            displayAllConfigs(response);
                        } else {
                            showAlert('error', response.message);
                            connectionTested = false;
                            $('#batchCheckBtn').prop('disabled', true);
                            $('.single-check-btn').prop('disabled', true);
                        }
                    },
                    error: function() {
                        showAlert('error', '连接测试失败，请检查网络连接');
                        connectionTested = false;
                        $('#batchCheckBtn').prop('disabled', true);
                        $('.single-check-btn').prop('disabled', true);
                    },
                    complete: function() {
                        hideLoading(btn);
                    }
                });
            });

            // 显示所有配置
            function displayAllConfigs(response) {
                // 显示业务主服务器配置行
                $('#mainServerRow').show();
                // 显示业务备服务器配置行
                $('#backupServerRow').show();
                // 显示采集主服务器配置行
                $('#collectMainServerRow').show();
                // 显示采集备服务器配置行
                $('#collectBackupServerRow').show();
                // 显示数据库配置行
                $('#databaseConfigRow').show();

                // 填充配置信息
                if (response.serverConfig) {
                    const config = response.serverConfig;
                    const vipPort = $('#vipPort').val();
                    const vipUsername = $('#vipUsername').val();
                    const vipPassword = $('#vipPassword').val();

                    if (config.localIP) {
                        $('#mainHost').val(config.localIP);
                        $('#mainPort').val(vipPort);
                        $('#mainUsername').val(vipUsername);
                        $('#mainPassword').val(vipPassword);
                    }
                    if (config.remoteIP) {
                        $('#backupHost').val(config.remoteIP);
                        $('#backupPort').val(vipPort);
                        $('#backupUsername').val(vipUsername);
                        $('#backupPassword').val(vipPassword);
                    }
                    if (config.masterFrontSCIP) {
                        $('#collectMainHost').val(config.masterFrontSCIP);
                        $('#collectMainPort').val(vipPort);
                        $('#collectMainUsername').val(vipUsername);
                        $('#collectMainPassword').val(vipPassword);
                    }
                    if (config.slaveFrontSCIP) {
                        $('#collectBackupHost').val(config.slaveFrontSCIP);
                        $('#collectBackupPort').val(vipPort);
                        $('#collectBackupUsername').val(vipUsername);
                        $('#collectBackupPassword').val(vipPassword);
                    }
                }

                // 填充数据库配置信息
                if (response.databaseConfig) {
                    displayDatabaseConfig(response.databaseConfig);
                }
            }
            
            // 批量检查
            $('#batchCheckBtn').click(function() {
                console.log('批量检查按钮被点击');
                if (!connectionTested) {
                    showAlert('error', '请先测试连接');
                    return;
                }

                const selectedItems = $('.check-item-input:checked').map(function() {
                    return $(this).val();
                }).get();

                if (selectedItems.length === 0) {
                    showAlert('error', '请选择至少一个检查项');
                    return;
                }

                const btn = $(this);
                showLoading(btn);

                // 重置所有检查项状态
                selectedItems.forEach(function(itemId) {
                    updateCheckItemStatus(itemId, 'checking', {
                        connecting: true
                    });
                });

                // 构建完整的配置信息
                const requestData = {
                    vipServer: {
                        name: '服务器VIP',
                        host: $('#vipHost').val(),
                        port: parseInt($('#vipPort').val()),
                        username: $('#vipUsername').val(),
                        password: $('#vipPassword').val(),
                        privateKeyPath: ''
                    },
                    mainServer: {
                        name: '业务主服务器',
                        host: $('#mainHost').val(),
                        port: parseInt($('#mainPort').val()),
                        username: $('#mainUsername').val(),
                        password: $('#mainPassword').val(),
                        privateKeyPath: ''
                    },
                    backupServer: {
                        name: '业务备服务器',
                        host: $('#backupHost').val(),
                        port: parseInt($('#backupPort').val()),
                        username: $('#backupUsername').val(),
                        password: $('#backupPassword').val(),
                        privateKeyPath: ''
                    },
                    collectMainServer: {
                        name: '采集主服务器',
                        host: $('#collectMainHost').val(),
                        port: parseInt($('#collectMainPort').val()),
                        username: $('#collectMainUsername').val(),
                        password: $('#collectMainPassword').val(),
                        privateKeyPath: ''
                    },
                    collectBackupServer: {
                        name: '采集备服务器',
                        host: $('#collectBackupHost').val(),
                        port: parseInt($('#collectBackupPort').val()),
                        username: $('#collectBackupUsername').val(),
                        password: $('#collectBackupPassword').val(),
                        privateKeyPath: ''
                    },
                    databaseConfig: {
                        url: $('#dbUrl').val(),
                        port: parseInt($('#dbPort').val()),
                        username: $('#dbUsername').val(),
                        password: $('#dbPassword').val(),
                        projectName: $('#dbProjectName').val(),
                        database: 'postgres'
                    },
                    checkItems: selectedItems
                };

                // 执行批量检查（一次性发送所有配置）
                executeBatchChecksWithAllConfigs(requestData, btn);
            });

            // 使用所有配置执行批量检查
            function executeBatchChecksWithAllConfigs(requestData, btn) {
                // 重置所有检查项状态
                requestData.checkItems.forEach(function(itemId) {
                    updateCheckItemStatus(itemId, 'checking');
                });

                $.ajax({
                    url: '/server-check/execute-checks',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(requestData),
                    success: function(response) {
                        if (response.success && response.results) {
                            // 更新每个检查项的结果
                            response.results.forEach(function(result) {
                                const status = result.success ? 'success' : 'error';
                                updateCheckItemStatus(result.checkItemId, status);

                                // 更新检查步骤详情
                                if (result.checkSteps) {
                                    updateCheckItemDetails(result.checkItemId, result.checkSteps);
                                }
                            });

                            // 显示总结
                            displayBatchSummary(response.summary);
                            showAlert('success', `批量检查完成：成功 ${response.summary.success} 项，失败 ${response.summary.fail} 项`);
                        } else {
                            showAlert('error', response.message || '批量检查失败');
                        }
                    },
                    error: function() {
                        showAlert('error', '批量检查请求失败，请检查网络连接');
                    },
                    complete: function() {
                        hideLoading(btn);
                    }
                });
            }

            // 显示批量检查总结
            function displayBatchSummary(summary) {
                const summaryHtml = `
                    <div class="alert alert-info mt-3">
                        <h6><i class="fas fa-chart-bar me-2"></i>批量检查总结</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>总计:</strong> ${summary.total} 项
                            </div>
                            <div class="col-md-3">
                                <strong>成功:</strong> <span class="text-success">${summary.success} 项</span>
                            </div>
                            <div class="col-md-3">
                                <strong>失败:</strong> <span class="text-danger">${summary.fail} 项</span>
                            </div>
                            <div class="col-md-3">
                                <strong>成功率:</strong> ${summary.total > 0 ? Math.round(summary.success / summary.total * 100) : 0}%
                            </div>
                        </div>
                    </div>
                `;

                // 移除现有的总结信息
                $('.server-config-card .alert-info').remove();

                // 添加新的总结信息
                $('.server-config-card .card-body').append(summaryHtml);
            }

            // 逐个执行检查（保留旧方法作为备用）
            function executeChecksSequentially(requestData, selectedItems, btn) {
                let currentIndex = 0;

                function executeNext() {
                    if (currentIndex >= selectedItems.length) {
                        // 所有检查完成，显示总结
                        displayOverallSummary();
                        hideLoading(btn);
                        return;
                    }

                    const itemId = selectedItems[currentIndex];

                    // 更新当前项状态
                    updateCheckItemStatus(itemId, 'checking', {
                        connected: true,
                        executing: true
                    });

                    // 执行单个检查
                    $.ajax({
                        url: '/server-check/execute-checks',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            serverInfo: requestData.serverInfo,
                            checkItems: [itemId]
                        }),
                        success: function(response) {
                            if (response.success && response.results.length > 0) {
                                const result = response.results[0];
                                checkResults[itemId] = result;

                                const status = result.success ? 'success' : 'error';
                                updateCheckItemStatus(itemId, status);

                                // 更新检查步骤详情
                                if (result.checkSteps) {
                                    updateCheckItemDetails(itemId, result.checkSteps);
                                }
                            } else {
                                checkResults[itemId] = {
                                    success: false,
                                    errorMessage: response.message || '检查失败'
                                };

                                updateCheckItemStatus(itemId, 'error');
                            }
                        },
                        error: function() {
                            checkResults[itemId] = {
                                success: false,
                                errorMessage: '网络错误或服务器无响应'
                            };

                            updateCheckItemStatus(itemId, 'error', {
                                connectError: true,
                                failed: true,
                                errorMessage: '网络错误或服务器无响应'
                            });
                        },
                        complete: function() {
                            currentIndex++;
                            // 延迟执行下一个，让用户看到过程
                            setTimeout(executeNext, 500);
                        }
                    });
                }

                executeNext();
            }

            // 显示总体摘要
            function displayOverallSummary() {
                const total = Object.keys(checkResults).length;
                const success = Object.values(checkResults).filter(r => r.success).length;
                const fail = total - success;
                const successRate = total > 0 ? Math.round(success / total * 100) : 0;

                const summaryHtml = `
                    <div class="stat-card" style="background: linear-gradient(45deg, #667eea, #764ba2);">
                        <h4>${total}</h4>
                        <small>总计</small>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(45deg, #28a745, #20c997);">
                        <h4>${success}</h4>
                        <small>成功</small>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(45deg, #dc3545, #e74c3c);">
                        <h4>${fail}</h4>
                        <small>失败</small>
                    </div>
                    <div class="stat-card" style="background: linear-gradient(45deg, #17a2b8, #20c997);">
                        <h4>${successRate}%</h4>
                        <small>成功率</small>
                    </div>
                `;

                $('#resultSummary').html(summaryHtml);
                $('#overallResults').show();

                // 滚动到结果区域
                $('html, body').animate({
                    scrollTop: $('#overallResults').offset().top - 100
                }, 500);
            }

            // 测试业务主服务器连接
            $('#testMainConnectionBtn').click(function() {
                testServerConnection(this, {
                    name: '业务主服务器',
                    host: $('#mainHost').val(),
                    port: parseInt($('#mainPort').val()),
                    username: $('#mainUsername').val(),
                    password: $('#mainPassword').val(),
                    privateKeyPath: ''
                });
            });

            // 测试业务备服务器连接
            $('#testBackupConnectionBtn').click(function() {
                testServerConnection(this, {
                    name: '业务备服务器',
                    host: $('#backupHost').val(),
                    port: parseInt($('#backupPort').val()),
                    username: $('#backupUsername').val(),
                    password: $('#backupPassword').val(),
                    privateKeyPath: ''
                });
            });

            // 测试采集主服务器连接
            $('#testCollectMainConnectionBtn').click(function() {
                testServerConnection(this, {
                    name: '采集主服务器',
                    host: $('#collectMainHost').val(),
                    port: parseInt($('#collectMainPort').val()),
                    username: $('#collectMainUsername').val(),
                    password: $('#collectMainPassword').val(),
                    privateKeyPath: ''
                });
            });

            // 测试采集备服务器连接
            $('#testCollectBackupConnectionBtn').click(function() {
                testServerConnection(this, {
                    name: '采集备服务器',
                    host: $('#collectBackupHost').val(),
                    port: parseInt($('#collectBackupPort').val()),
                    username: $('#collectBackupUsername').val(),
                    password: $('#collectBackupPassword').val(),
                    privateKeyPath: ''
                });
            });

            // 通用服务器连接测试函数
            function testServerConnection(button, serverInfo) {
                const btn = $(button);
                showLoading(btn);

                $.ajax({
                    url: '/server-check/test-connection',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(serverInfo),
                    success: function(response) {
                        if (response.success) {
                            showAlert('success', `${serverInfo.name}连接成功`);
                        } else {
                            showAlert('error', `${serverInfo.name}连接失败: ${response.message}`);
                        }
                    },
                    error: function() {
                        showAlert('error', `${serverInfo.name}连接测试失败，请检查网络连接`);
                    },
                    complete: function() {
                        hideLoading(btn);
                    }
                });
            }

            // 显示数据库配置信息
            function displayDatabaseConfig(dbConfig) {
                // 填充数据库配置表单
                $('#dbUrl').val(dbConfig.url || '');
                $('#dbPort').val(dbConfig.port || '');
                $('#dbUsername').val(dbConfig.username || '');
                $('#dbPassword').val(dbConfig.password || '');
                $('#dbProjectName').val(dbConfig.projectName || '');

                // 显示连接状态提示
                const statusHtml = `
                    <div class="alert ${dbConfig.connected ? 'alert-success' : 'alert-danger'} mt-2">
                        <small><i class="fas fa-database me-1"></i>数据库连接状态:
                        <span class="badge ${dbConfig.connected ? 'bg-success' : 'bg-danger'}">
                            ${dbConfig.connected ? '连接成功' : '连接失败'}
                        </span>
                        ${dbConfig.errorMessage ? ` - ${dbConfig.errorMessage}` : ''}
                        ${dbConfig.projectName ? ` | 项目名: ${dbConfig.projectName}` : ''}
                        </small>
                    </div>
                `;

                // 移除现有的数据库状态信息
                $('.server-config-card .alert').remove();

                // 添加新的数据库状态信息
                $('.server-config-card .card-body').append(statusHtml);
            }

            // 单个检查功能
            $(document).on('click', '.single-check-btn', function() {
                if (!connectionTested) {
                    showAlert('error', '请先测试连接');
                    return;
                }

                const btn = $(this);
                const itemId = btn.data('item-id');
                console.log('单个检查按钮被点击，项目ID:', itemId);

                // 禁用按钮并显示加载状态
                const originalText = btn.html();
                btn.html('<span class="loading"></span>').prop('disabled', true);

                // 更新检查项状态
                updateCheckItemStatus(itemId, 'checking', {
                    connecting: true
                });

                // 构建完整的配置信息
                const requestData = {
                    vipServer: {
                        name: '服务器VIP',
                        host: $('#vipHost').val(),
                        port: parseInt($('#vipPort').val()),
                        username: $('#vipUsername').val(),
                        password: $('#vipPassword').val(),
                        privateKeyPath: ''
                    },
                    mainServer: {
                        name: '业务主服务器',
                        host: $('#mainHost').val(),
                        port: parseInt($('#mainPort').val()),
                        username: $('#mainUsername').val(),
                        password: $('#mainPassword').val(),
                        privateKeyPath: ''
                    },
                    backupServer: {
                        name: '业务备服务器',
                        host: $('#backupHost').val(),
                        port: parseInt($('#backupPort').val()),
                        username: $('#backupUsername').val(),
                        password: $('#backupPassword').val(),
                        privateKeyPath: ''
                    },
                    collectMainServer: {
                        name: '采集主服务器',
                        host: $('#collectMainHost').val(),
                        port: parseInt($('#collectMainPort').val()),
                        username: $('#collectMainUsername').val(),
                        password: $('#collectMainPassword').val(),
                        privateKeyPath: ''
                    },
                    collectBackupServer: {
                        name: '采集备服务器',
                        host: $('#collectBackupHost').val(),
                        port: parseInt($('#collectBackupPort').val()),
                        username: $('#collectBackupUsername').val(),
                        password: $('#collectBackupPassword').val(),
                        privateKeyPath: ''
                    },
                    databaseConfig: {
                        url: $('#dbUrl').val(),
                        port: parseInt($('#dbPort').val()),
                        username: $('#dbUsername').val(),
                        password: $('#dbPassword').val(),
                        projectName: $('#dbProjectName').val(),
                        database: 'postgres'
                    },
                    checkItems: [itemId]
                };

                // 执行单个检查
                executeSingleCheckWithAllConfigs(requestData, function() {
                    // 检查完成后恢复按钮状态
                    btn.html(originalText).prop('disabled', false);
                });
            });

            // 使用所有配置执行单个检查
            function executeSingleCheckWithAllConfigs(requestData, callback) {
                const itemId = requestData.checkItems[0];

                // 更新状态为执行中
                updateCheckItemStatus(itemId, 'checking');

                $.ajax({
                    url: '/server-check/execute-checks',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(requestData),
                    success: function(response) {
                        if (response.success && response.results.length > 0) {
                            const result = response.results[0];
                            checkResults[itemId] = result;

                            const status = result.success ? 'success' : 'error';
                            updateCheckItemStatus(itemId, status);

                            // 更新检查步骤详情
                            if (result.checkSteps) {
                                updateCheckItemDetails(itemId, result.checkSteps);
                            }
                        } else {
                            checkResults[itemId] = {
                                success: false,
                                errorMessage: response.message || '检查失败'
                            };

                            updateCheckItemStatus(itemId, 'error');
                        }
                    },
                    error: function() {
                        checkResults[itemId] = {
                            success: false,
                            errorMessage: '网络错误或服务器无响应'
                        };

                        updateCheckItemStatus(itemId, 'error');
                    },
                    complete: function() {
                        if (callback) callback();
                    }
                });
            }

            // 执行单个检查的函数（保留旧方法作为备用）
            function executeSingleCheck(serverInfo, itemId, callback) {
                // 更新状态为执行中
                updateCheckItemStatus(itemId, 'checking', {
                    connected: true,
                    executing: true
                });

                $.ajax({
                    url: '/server-check/execute-checks',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        serverInfo: serverInfo,
                        checkItems: [itemId]
                    }),
                    success: function(response) {
                        if (response.success && response.results.length > 0) {
                            const result = response.results[0];
                            checkResults[itemId] = result;

                            const status = result.success ? 'success' : 'error';
                            updateCheckItemStatus(itemId, status);

                            // 更新检查步骤详情
                            if (result.checkSteps) {
                                updateCheckItemDetails(itemId, result.checkSteps);
                            }
                        } else {
                            checkResults[itemId] = {
                                success: false,
                                errorMessage: response.message || '检查失败'
                            };

                            updateCheckItemStatus(itemId, 'error');
                        }
                    },
                    error: function() {
                        checkResults[itemId] = {
                            success: false,
                            errorMessage: '网络错误或服务器无响应'
                        };

                        updateCheckItemStatus(itemId, 'error', {
                            connectError: true,
                            failed: true,
                            errorMessage: '网络错误或服务器无响应'
                        });
                    },
                    complete: function() {
                        if (callback) callback();
                    }
                });
            }

            // 详情展开/收起功能
            $(document).on('click', '.toggle-details', function() {
                const card = $(this).closest('.check-item-card');
                const details = card.find('.check-process-details');
                const icon = $(this).find('i');

                if (details.is(':visible')) {
                    details.slideUp();
                    icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                    $(this).html('<i class="fas fa-chevron-down me-1"></i>详情');
                } else {
                    details.slideDown();
                    icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
                    $(this).html('<i class="fas fa-chevron-up me-1"></i>收起');
                }
            });

            // 全选/清空
            $('#selectAllBtn').click(function() {
                $('.check-item-input').prop('checked', true);
                $('.check-item-card').addClass('selected');
            });

            $('#clearAllBtn').click(function() {
                $('.check-item-input').prop('checked', false);
                $('.check-item-card').removeClass('selected');
            });

            // 检查项选择状态
            $(document).on('change', '.check-item-input', function() {
                const card = $(this).closest('.check-item-card');
                if ($(this).is(':checked')) {
                    card.addClass('selected');
                } else {
                    card.removeClass('selected');
                }
            });

            // 测试数据库连接
            $('#testDbConnectionBtn').click(function() {
                const requiredFields = ['#dbUrl', '#dbPort', '#dbUsername', '#dbPassword'];
                let isValid = true;

                requiredFields.forEach(function(field) {
                    if (!$(field).val().trim()) {
                        $(field).addClass('is-invalid');
                        isValid = false;
                    } else {
                        $(field).removeClass('is-invalid');
                    }
                });

                if (!isValid) {
                    showAlert('error', '请填写所有必填字段');
                    return;
                }

                const btn = $(this);
                showLoading(btn);

                const dbConfig = {
                    url: $('#dbUrl').val(),
                    port: parseInt($('#dbPort').val()),
                    username: $('#dbUsername').val(),
                    password: $('#dbPassword').val(),
                    projectName: $('#dbProjectName').val()
                };

                $.ajax({
                    url: '/server-check/test-database',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(dbConfig),
                    success: function(response) {
                        if (response.success) {
                            showAlert('success', response.message);
                        } else {
                            showAlert('error', response.message);
                        }
                    },
                    error: function() {
                        showAlert('error', '数据库连接测试失败，请检查网络连接');
                    },
                    complete: function() {
                        hideLoading(btn);
                    }
                });
            });
            
            // 分类筛选
            $('#categoryFilter').change(function() {
                const selectedCategory = $(this).val();
                if (selectedCategory) {
                    $('.category-section').hide();
                    $('.category-section').filter(function() {
                        return $(this).find('.category-title').text() === selectedCategory;
                    }).show();
                } else {
                    $('.category-section').show();
                }
            });
        });
    </script>
</body>
</html>
