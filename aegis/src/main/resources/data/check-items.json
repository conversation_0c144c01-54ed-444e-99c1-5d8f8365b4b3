[{"id": "db_alarm_count", "name": "当月告警统计", "description": "检查当月告警是否超过300万", "category": "数据库", "command": "SELECT COUNT(*) as alarm_count FROM pecalarmextend WHERE eventtime >= ? AND eventtime < ?", "checkType": "DATABASE", "enabled": true}, {"id": "docker_version_business", "name": "业务服务器程序版本一致性", "description": "检查业务主备服务器运行的程序版本是否一致", "category": "服务", "command": "docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort", "checkType": "MULTI_SERVER", "enabled": true}, {"id": "docker_version_collect", "name": "采集服务器程序版本一致性", "description": "检查采集主备服务器运行的程序版本是否一致", "category": "服务", "command": "docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort", "checkType": "MULTI_SERVER", "enabled": true}, {"id": "device_data_service_version", "name": "设备数据服务版本检查", "description": "检查设备数据服务版本是否需要更新", "category": "服务", "command": "docker ps --format 'table {{.Image}}' | grep device-data-service", "checkType": "MULTI_SERVER", "enabled": true}, {"id": "pecdeviceextend_duplicate", "name": "设备数据重复检查", "description": "检查是否存在重复的设备数据", "category": "数据库", "command": "SELECT deviceid, COUNT(*) as count FROM pecdeviceextend GROUP BY deviceid HAVING COUNT(*) > 1", "checkType": "DATABASE", "enabled": true}, {"id": "alarm_point_underscore", "name": "告警点名称规范检查", "description": "检查告警点名称是否符合命名规范", "category": "数据库", "command": "SELECT dataname FROM thresholdmeasurepoint WHERE dataname LIKE '%_%'", "checkType": "DATABASE", "enabled": true}, {"id": "server_time_diff_business", "name": "业务服务器时间同步检查", "description": "检查业务主备服务器时间是否同步", "category": "系统信息", "command": "date +%s", "checkType": "MULTI_SERVER", "enabled": true}, {"id": "server_time_diff_collect", "name": "采集服务器时间同步检查", "description": "检查采集主备服务器时间是否同步", "category": "系统信息", "command": "date +%s", "checkType": "MULTI_SERVER", "enabled": true}, {"id": "backup_service_status", "name": "数据备份服务检查", "description": "检查数据备份服务是否正常运行", "category": "服务", "command": "systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive'", "checkType": "MULTI_SERVER", "enabled": true}, {"id": "project_name_check", "name": "项目配置格式检查", "description": "检查项目配置格式是否正确", "category": "配置", "command": "", "checkType": "CONFIG_CHECK", "enabled": true}, {"id": "server_resource_usage", "name": "服务器性能检查", "description": "检查服务器CPU、内存、磁盘使用情况", "category": "系统信息", "command": "echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && free -h | awk 'NR==2{printf \"Memory:%s/%s(%.1f%%);\",$3,$2,$3*100/$2}' && df -h /var | awk 'NR==2{printf \"Disk:%s/%s(%s);\",$3,$2,$5}'", "checkType": "MULTI_SERVER", "enabled": true}, {"id": "service_log_exception", "name": "服务日志异常检查", "description": "检查Docker服务日志中的异常信息", "category": "服务", "command": "docker ps --format '{{.Names}}' | head -10", "checkType": "MULTI_SERVER", "enabled": true}, {"id": "ssh_passwordless_business", "name": "业务服务器免密登录检查", "description": "检查业务主备服务器之间是否可以免密登录", "category": "系统信息", "command": "ssh -o BatchMode=yes -o ConnectTimeout=5", "checkType": "MULTI_SERVER", "enabled": true}, {"id": "file_sync_business", "name": "业务服务器文件同步检查", "description": "检查业务主备服务器文件管理器目录同步情况", "category": "系统信息", "command": "ls -1 /var/cache/CET/filemanager/ 2>/dev/null | sort", "checkType": "MULTI_SERVER", "enabled": true}, {"id": "alarm_auth_push_check", "name": "告警权限推送检查", "description": "检查告警是否开启按权限推送功能", "category": "配置", "command": "grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'cet_auth-push'", "checkType": "CONFIG_CHECK", "enabled": true}, {"id": "alarm_subscription_check", "name": "告警订阅功能检查", "description": "检查告警订阅功能是否开启", "category": "配置", "command": "grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'sms_swtich'", "checkType": "CONFIG_CHECK", "enabled": true}, {"id": "onlyreport_config_check", "name": "报表配置文件检查", "description": "检查报表配置文件是否完整", "category": "配置", "command": "ls /etc/CET/only-report/config/", "checkType": "CONFIG_CHECK", "enabled": true}, {"id": "docker_compose_tab_check", "name": "Docker配置文件格式检查", "description": "检查docker-compose.yml文件是否包含tab符", "category": "配置", "command": "grep -n $'\\t' /etc/CET/docker/docker-compose.yml", "checkType": "CONFIG_CHECK", "enabled": true}, {"id": "netclient_version_check", "name": "网络客户端版本检查", "description": "检查netclient版本是否需要更新", "category": "服务", "command": "docker ps --format 'table {{.Image}}' | grep netclient", "checkType": "MULTI_SERVER", "enabled": true}]