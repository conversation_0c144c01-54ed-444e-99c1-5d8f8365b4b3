[{"id": "sys_high_load", "title": "系统负载过高", "description": "系统负载过高导致响应缓慢的排查和解决方案", "category": "系统", "keywords": ["负载", "load", "cpu", "性能", "缓慢"], "solutionFile": "system-high-load.md", "color": "#ff6b6b", "priority": 10, "enabled": true}, {"id": "sys_memory_full", "title": "内存不足", "description": "系统内存不足或内存泄漏的排查和解决方案", "category": "系统", "keywords": ["内存", "memory", "oom", "swap", "泄漏"], "solutionFile": "system-memory-full.md", "color": "#4ecdc4", "priority": 9, "enabled": true}, {"id": "sys_disk_full", "title": "磁盘空间不足", "description": "磁盘空间不足的排查和清理方案", "category": "系统", "keywords": ["磁盘", "disk", "空间", "满", "清理"], "solutionFile": "system-disk-full.md", "color": "#45b7d1", "priority": 8, "enabled": true}, {"id": "net_connection_fail", "title": "网络连接失败", "description": "网络连接问题的排查和解决方案", "category": "网络", "keywords": ["网络", "连接", "ping", "dns", "超时"], "solutionFile": "network-connection-fail.md", "color": "#96ceb4", "priority": 7, "enabled": true}, {"id": "service_down", "title": "服务停止", "description": "系统服务停止的排查和重启方案", "category": "服务", "keywords": ["服务", "service", "停止", "启动", "重启"], "solutionFile": "service-down.md", "color": "#feca57", "priority": 6, "enabled": true}, {"id": "ssh_connection_fail", "title": "SSH连接失败", "description": "SSH连接失败的常见原因和解决方案", "category": "网络", "keywords": ["ssh", "连接", "认证", "密钥", "端口"], "solutionFile": "ssh-connection-fail.md", "color": "#ff9ff3", "priority": 5, "enabled": true}, {"id": "file_permission_error", "title": "文件权限错误", "description": "文件权限问题的排查和修复方案", "category": "系统", "keywords": ["权限", "permission", "chmod", "chown", "访问"], "solutionFile": "file-permission-error.md", "color": "#54a0ff", "priority": 4, "enabled": true}, {"id": "process_zombie", "title": "僵尸进程", "description": "僵尸进程的识别和清理方案", "category": "系统", "keywords": ["僵尸", "zombie", "进程", "清理"], "solutionFile": "process-zombie.md", "color": "#5f27cd", "priority": 3, "enabled": true}, {"id": "log_analysis", "title": "日志分析", "description": "系统日志分析的方法和工具", "category": "日志", "keywords": ["日志", "log", "分析", "错误", "排查"], "solutionFile": "log-analysis.md", "color": "#00d2d3", "priority": 2, "enabled": true}, {"id": "backup_restore", "title": "备份与恢复", "description": "数据备份和系统恢复的最佳实践", "category": "运维", "keywords": ["备份", "恢复", "数据", "灾难", "恢复"], "solutionFile": "backup-restore.md", "color": "#ff6348", "priority": 1, "enabled": true}]