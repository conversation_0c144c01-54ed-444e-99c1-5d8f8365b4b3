# 系统内存不足解决方案

## 问题描述

内存不足的常见表现：
- 系统运行缓慢
- 应用程序崩溃
- OOM (Out of Memory) 错误
- 大量使用swap空间

## 排查步骤

### 1. 检查内存使用情况

```bash
# 查看内存总体使用情况
free -h

# 详细内存信息
cat /proc/meminfo

# 查看内存使用趋势
sar -r 1 10
```

### 2. 找出内存占用大的进程

```bash
# 按内存使用率排序
ps aux --sort=-%mem | head -20

# 实时监控内存使用
top -o %MEM

# 查看进程内存详情
pmap <PID>
```

### 3. 检查系统日志

```bash
# 查看OOM日志
dmesg | grep -i "killed process"
grep -i "out of memory" /var/log/messages
```

### 4. 分析内存泄漏

```bash
# 使用valgrind检查内存泄漏
valgrind --tool=memcheck --leak-check=full <program>

# 查看进程内存映射
cat /proc/<PID>/smaps
```

## 解决方案

### 立即解决方案

1. **释放缓存**
   ```bash
   # 释放页面缓存
   sync && echo 1 > /proc/sys/vm/drop_caches
   
   # 释放dentries和inodes
   sync && echo 2 > /proc/sys/vm/drop_caches
   
   # 释放所有缓存
   sync && echo 3 > /proc/sys/vm/drop_caches
   ```

2. **终止高内存使用进程**
   ```bash
   # 找到内存使用最高的进程
   ps aux --sort=-%mem | head -5
   
   # 终止进程
   kill -9 <PID>
   ```

3. **增加swap空间**
   ```bash
   # 创建swap文件
   dd if=/dev/zero of=/swapfile bs=1024 count=2097152
   chmod 600 /swapfile
   mkswap /swapfile
   swapon /swapfile
   
   # 永久生效
   echo '/swapfile swap swap defaults 0 0' >> /etc/fstab
   ```

### 长期解决方案

1. **优化应用程序**
   - 修复内存泄漏
   - 优化数据结构
   - 实现内存池
   - 及时释放资源

2. **系统调优**
   ```bash
   # 调整swappiness
   echo 'vm.swappiness=10' >> /etc/sysctl.conf
   
   # 调整内存回收策略
   echo 'vm.vfs_cache_pressure=50' >> /etc/sysctl.conf
   
   # 应用配置
   sysctl -p
   ```

3. **硬件升级**
   - 增加物理内存
   - 使用更快的存储设备
   - 优化服务器配置

## 内存监控

### 设置监控脚本

```bash
#!/bin/bash
# memory_monitor.sh

THRESHOLD=90
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.0f", $3/$2 * 100)}')

if [ $MEMORY_USAGE -gt $THRESHOLD ]; then
    echo "警告：内存使用率达到 ${MEMORY_USAGE}%"
    # 发送告警邮件或短信
    # mail -s "内存告警" <EMAIL> < /dev/null
fi
```

### 定期清理

```bash
# 添加到crontab
# 每小时清理一次缓存
0 * * * * sync && echo 1 > /proc/sys/vm/drop_caches

# 每天重启高内存使用的服务
0 2 * * * systemctl restart <service_name>
```

## 预防措施

1. **容量规划**
   - 评估应用内存需求
   - 预留足够的内存空间
   - 定期检查内存趋势

2. **应用优化**
   - 代码审查
   - 性能测试
   - 内存泄漏检测

3. **监控告警**
   - 设置内存使用率告警
   - 监控关键进程
   - 定期生成报告

## 常用命令

| 命令 | 说明 |
|------|------|
| `free -h` | 查看内存使用情况 |
| `top` | 实时查看进程内存使用 |
| `ps aux --sort=-%mem` | 按内存使用排序进程 |
| `pmap <PID>` | 查看进程内存映射 |
| `vmstat` | 虚拟内存统计 |
| `sar -r` | 内存使用历史 |

## 注意事项

⚠️ **重要提醒**：
- 清理缓存会暂时影响系统性能
- 终止进程前确认不会影响业务
- 增加swap会影响系统性能
- 内存不足可能导致数据丢失
