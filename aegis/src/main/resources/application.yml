server:
  port: 8080
  servlet:
    context-path: /
  tomcat:
    max-threads: 200
    min-spare-threads: 10

spring:
  application:
    name: aegis
  thymeleaf:
    cache: false
    encoding: UTF-8
    mode: HTML
    prefix: classpath:/templates/
    suffix: .html
  web:
    resources:
      static-locations: classpath:/static/
      cache:
        period: 0
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8

logging:
  config: classpath:logback-spring.xml
  level:
    com.cet.datacenter: INFO
    org.springframework: WARN
    org.thymeleaf: WARN

# 应用配置
aegis:
  # SSH连接配置
  ssh:
    timeout: 30000
    retry-count: 3
  # 日志配置
  log:
    path: logs
    max-file-size: 10MB
    max-history: 30
  # 知识库配置
  knowledge:
    data-path: classpath:data/knowledge-base.json
    solutions-path: classpath:data/solutions/
  # 检查项配置
  check:
    items-path: classpath:data/check-items.json
