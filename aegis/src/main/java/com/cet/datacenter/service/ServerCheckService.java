package com.cet.datacenter.service;

import com.cet.datacenter.entity.CheckExecutionRequest;
import com.cet.datacenter.entity.CheckItem;
import com.cet.datacenter.entity.CheckResult;
import com.cet.datacenter.entity.CheckStep;
import com.cet.datacenter.entity.DatabaseConfig;
import com.cet.datacenter.entity.ServerInfo;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 服务器检查服务类
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@Service
public class ServerCheckService {
    
    private static final Logger logger = LoggerFactory.getLogger(ServerCheckService.class);
    private static final Logger checkLogger = LoggerFactory.getLogger("SYSTEM_CHECK");
    
    @Autowired
    private SshService sshService;

    @Autowired
    private DatabaseService databaseService;
    
    @Autowired
    private ResourceLoader resourceLoader;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Value("${aegis.check.items-path}")
    private String checkItemsPath;
    
    private List<CheckItem> checkItems = new ArrayList<>();
    
    @PostConstruct
    public void init() {
        loadCheckItems();
    }
    
    /**
     * 加载检查项配置
     */
    private void loadCheckItems() {
        try {
            Resource resource = resourceLoader.getResource(checkItemsPath);
            if (resource.exists()) {
                checkItems = objectMapper.readValue(resource.getInputStream(), 
                        new TypeReference<List<CheckItem>>() {});
                logger.info("加载检查项配置成功，共{}个检查项", checkItems.size());
            } else {
                logger.warn("检查项配置文件不存在: {}", checkItemsPath);
                createDefaultCheckItems();
            }
        } catch (IOException e) {
            logger.error("加载检查项配置失败: {}", e.getMessage());
            createDefaultCheckItems();
        }
    }
    
    /**
     * 创建默认检查项
     */
    private void createDefaultCheckItems() {
        checkItems = new ArrayList<>();
        
        // 系统信息检查
        checkItems.add(new CheckItem("sys_uptime", "系统运行时间", "检查系统运行时间", "系统", "uptime", "COMMAND"));
        checkItems.add(new CheckItem("sys_load", "系统负载", "检查系统负载", "系统", "cat /proc/loadavg", "COMMAND"));
        checkItems.add(new CheckItem("sys_memory", "内存使用情况", "检查内存使用情况", "系统", "free -h", "COMMAND"));
        checkItems.add(new CheckItem("sys_disk", "磁盘使用情况", "检查磁盘使用情况", "系统", "df -h", "COMMAND"));
        
        // 网络检查
        checkItems.add(new CheckItem("net_interface", "网络接口", "检查网络接口状态", "网络", "ip addr show", "COMMAND"));
        checkItems.add(new CheckItem("net_route", "路由表", "检查路由表", "网络", "ip route show", "COMMAND"));
        
        // 服务检查
        checkItems.add(new CheckItem("service_ssh", "SSH服务", "检查SSH服务状态", "服务", "systemctl status sshd", "COMMAND"));

        // 数据库检查
        checkItems.add(new CheckItem("db_connection", "数据库连接", "检查PostgreSQL数据库连接", "数据库", "", "DATABASE"));
        checkItems.add(new CheckItem("db_version", "数据库版本", "查询PostgreSQL版本信息", "数据库", "SELECT version();", "DATABASE"));
        checkItems.add(new CheckItem("db_tables", "数据表统计", "统计数据库中的表数量", "数据库", "SELECT schemaname, COUNT(*) as table_count FROM pg_tables WHERE schemaname NOT IN ('information_schema', 'pg_catalog') GROUP BY schemaname;", "DATABASE"));
        checkItems.add(new CheckItem("db_alarm_count", "当月告警统计", "检查当月告警是否超过300万", "数据库", "SELECT COUNT(*) as alarm_count FROM pecalarmextend WHERE eventtime >= ? AND eventtime < ?", "DATABASE"));

        logger.info("创建默认检查项成功，共{}个检查项", checkItems.size());
    }
    
    /**
     * 获取所有检查项
     */
    public List<CheckItem> getAllCheckItems() {
        return checkItems.stream()
                .filter(CheckItem::isEnabled)
                .collect(Collectors.toList());
    }
    
    /**
     * 根据分类获取检查项
     */
    public List<CheckItem> getCheckItemsByCategory(String category) {
        return checkItems.stream()
                .filter(item -> item.isEnabled() && category.equals(item.getCategory()))
                .collect(Collectors.toList());
    }
    
    /**
     * 执行单个检查项
     */
    public CheckResult executeCheckItem(ServerInfo serverInfo, CheckItem checkItem) {
        CheckResult result = new CheckResult(checkItem.getId(), checkItem.getName(), serverInfo.getHost());
        result.setExpectedResult(checkItem.getExpectedResult());

        long startTime = System.currentTimeMillis();

        try {
            checkLogger.info("开始执行检查: 服务器={}, 检查项={}", serverInfo.getHost(), checkItem.getName());

            boolean success = false;
            String actualResult = "";

            switch (checkItem.getCheckType()) {
                case "COMMAND":
                    success = executeCommandCheck(serverInfo, checkItem, result);
                    break;

                case "DATABASE":
                    success = executeDatabaseCheck(serverInfo, checkItem, result);
                    break;

                case "FILE_EXISTS":
                    success = executeFileExistsCheck(serverInfo, checkItem, result);
                    break;

                case "PROCESS_RUNNING":
                    success = executeProcessCheck(serverInfo, checkItem, result);
                    break;

                case "PORT_LISTENING":
                    success = executePortCheck(serverInfo, checkItem, result);
                    break;

                default:
                    CheckStep errorStep = new CheckStep("错误", "不支持的检查类型", "ERROR");
                    errorStep.setSuccess(false);
                    errorStep.setErrorMessage("不支持的检查类型: " + checkItem.getCheckType());
                    result.addCheckStep(errorStep);
                    success = false;
            }

            result.setSuccess(success);

            checkLogger.info("检查完成: 服务器={}, 检查项={}, 结果={}",
                    serverInfo.getHost(), checkItem.getName(), success ? "成功" : "失败");

        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setActualResult("执行异常: " + e.getMessage());

            CheckStep errorStep = new CheckStep("异常", "执行过程中发生异常", "ERROR");
            errorStep.setSuccess(false);
            errorStep.setErrorMessage(e.getMessage());
            result.addCheckStep(errorStep);

            checkLogger.error("检查异常: 服务器={}, 检查项={}, 错误={}",
                    serverInfo.getHost(), checkItem.getName(), e.getMessage());
        }

        long endTime = System.currentTimeMillis();
        result.setExecutionTime(endTime - startTime);

        return result;
    }
    
    /**
     * 执行多个检查项（新版本，支持所有配置）
     */
    public List<CheckResult> executeCheckItems(CheckExecutionRequest request) {
        List<CheckResult> results = new ArrayList<>();

        logger.info("开始执行检查，检查项数量: {}", request.getCheckItems().size());

        // 建立连接缓存，避免重复连接
        Map<String, Boolean> serverConnections = new HashMap<>();
        Map<String, Boolean> databaseConnections = new HashMap<>();

        for (String itemId : request.getCheckItems()) {
            CheckItem checkItem = findCheckItemById(itemId);
            if (checkItem != null) {
                CheckResult result = executeCheckItemWithRequest(request, checkItem, serverConnections, databaseConnections);
                results.add(result);
            } else {
                logger.warn("未找到检查项: {}", itemId);
                CheckResult errorResult = new CheckResult(itemId, "未知检查项", "未知");
                errorResult.setSuccess(false);
                errorResult.setErrorMessage("未找到检查项: " + itemId);
                results.add(errorResult);
            }
        }

        return results;
    }

    /**
     * 执行多个检查项（旧版本，保持兼容性）
     */
    public List<CheckResult> executeCheckItems(ServerInfo serverInfo, List<String> checkItemIds) {
        List<CheckResult> results = new ArrayList<>();

        for (String checkItemId : checkItemIds) {
            CheckItem checkItem = findCheckItemById(checkItemId);
            if (checkItem != null) {
                CheckResult result = executeCheckItem(serverInfo, checkItem);
                results.add(result);
            } else {
                CheckResult result = new CheckResult(checkItemId, "未知检查项", serverInfo.getHost());
                result.setSuccess(false);
                result.setErrorMessage("检查项不存在: " + checkItemId);
                results.add(result);
            }
        }

        return results;
    }

    /**
     * 使用完整请求执行单个检查项（支持连接复用）
     */
    private CheckResult executeCheckItemWithRequest(CheckExecutionRequest request, CheckItem checkItem,
                                                   Map<String, Boolean> serverConnections,
                                                   Map<String, Boolean> databaseConnections) {
        CheckResult result = new CheckResult(checkItem.getId(), checkItem.getName(), "多服务器");
        result.setExpectedResult(checkItem.getExpectedResult());

        long startTime = System.currentTimeMillis();

        try {
            checkLogger.info("开始执行检查: 检查项={}", checkItem.getName());

            boolean success = false;

            switch (checkItem.getCheckType()) {
                case "COMMAND":
                    // 命令检查默认使用VIP服务器
                    success = executeCommandCheckWithRequest(request.getVipServer(), checkItem, result, serverConnections);
                    break;

                case "DATABASE":
                    success = executeDatabaseCheckWithRequest(request, checkItem, result, serverConnections, databaseConnections);
                    break;

                case "FILE_EXISTS":
                    success = executeFileExistsCheckWithRequest(request.getVipServer(), checkItem, result, serverConnections);
                    break;

                case "PROCESS_RUNNING":
                    success = executeProcessCheckWithRequest(request.getVipServer(), checkItem, result, serverConnections);
                    break;

                case "PORT_LISTENING":
                    success = executePortCheckWithRequest(request.getVipServer(), checkItem, result, serverConnections);
                    break;

                case "MULTI_SERVER":
                    success = executeMultiServerCheck(request, checkItem, result, serverConnections);
                    break;

                case "CONFIG_CHECK":
                    success = executeConfigCheck(request, checkItem, result);
                    break;

                default:
                    CheckStep errorStep = new CheckStep("错误", "不支持的检查类型", "ERROR");
                    errorStep.setSuccess(false);
                    errorStep.setErrorMessage("不支持的检查类型: " + checkItem.getCheckType());
                    result.addCheckStep(errorStep);
                    success = false;
            }

            result.setSuccess(success);

            checkLogger.info("检查完成: 检查项={}, 结果={}", checkItem.getName(), success ? "成功" : "失败");

        } catch (Exception e) {
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setActualResult("执行异常: " + e.getMessage());

            CheckStep errorStep = new CheckStep("异常", "执行过程中发生异常", "ERROR");
            errorStep.setSuccess(false);
            errorStep.setErrorMessage(e.getMessage());
            result.addCheckStep(errorStep);

            checkLogger.error("检查异常: 检查项={}, 错误={}", checkItem.getName(), e.getMessage());
        }

        long endTime = System.currentTimeMillis();
        result.setExecutionTime(endTime - startTime);

        return result;
    }

    /**
     * 执行命令检查
     */
    private boolean executeCommandCheck(ServerInfo serverInfo, CheckItem checkItem, CheckResult result) {
        // 步骤1：连接服务器
        CheckStep connectStep = new CheckStep("连接服务器", "建立SSH连接", "SSH_CONNECT");
        connectStep.setStatus("RUNNING");
        result.addCheckStep(connectStep);

        try {
            boolean connected = sshService.testConnection(serverInfo);
            connectStep.setSuccess(connected);
            connectStep.setResult(connected ? "连接成功" : "连接失败");

            if (!connected) {
                return false;
            }

            // 步骤2：执行命令
            CheckStep commandStep = new CheckStep("执行命令", "执行系统命令", "SSH_COMMAND");
            commandStep.setCommand(checkItem.getCommand());
            commandStep.setStatus("RUNNING");
            result.addCheckStep(commandStep);

            String commandResult = sshService.executeCommand(serverInfo, checkItem.getCommand());
            commandStep.setSuccess(true);
            commandStep.setResult(commandResult);

            // 步骤3：分析结果
            CheckStep analysisStep = new CheckStep("分析结果", "分析命令执行结果", "ANALYSIS");
            analysisStep.setStatus("RUNNING");
            result.addCheckStep(analysisStep);

            analysisStep.setSuccess(true);
            analysisStep.setResult("命令执行成功");
            result.setActualResult(commandResult);

            return true;

        } catch (Exception e) {
            connectStep.setSuccess(false);
            connectStep.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 执行数据库检查
     */
    private boolean executeDatabaseCheck(ServerInfo serverInfo, CheckItem checkItem, CheckResult result) {
        // 对于告警统计检查，直接连接数据库，不需要连接服务器
        if ("db_alarm_count".equals(checkItem.getId())) {
            return executeAlarmCountCheckDirect(result);
        }

        // 其他数据库检查需要先连接服务器读取配置
        // 步骤1：连接服务器
        CheckStep connectStep = new CheckStep("连接服务器", "建立SSH连接", "SSH_CONNECT");
        connectStep.setStatus("RUNNING");
        result.addCheckStep(connectStep);

        try {
            boolean connected = sshService.testConnection(serverInfo);
            connectStep.setSuccess(connected);
            connectStep.setResult(connected ? "连接成功" : "连接失败");

            if (!connected) {
                return false;
            }

            // 步骤2：读取数据库配置
            CheckStep configStep = new CheckStep("读取数据库配置", "从.env文件读取数据库配置", "SSH_COMMAND");
            configStep.setCommand("cat /etc/CET/docker/.env | grep -E 'DataBase(Url|Port|User|Pwd)'");
            configStep.setStatus("RUNNING");
            result.addCheckStep(configStep);

            DatabaseConfig dbConfig = readDatabaseConfig(serverInfo);
            if (dbConfig == null) {
                configStep.setSuccess(false);
                configStep.setErrorMessage("无法读取数据库配置");
                return false;
            }

            configStep.setSuccess(true);
            configStep.setResult("数据库配置读取成功: " + dbConfig.getUrl() + ":" + dbConfig.getPort());

            // 步骤3：连接数据库
            CheckStep dbConnectStep = new CheckStep("连接数据库", "建立PostgreSQL数据库连接", "DB_CONNECT");
            dbConnectStep.setStatus("RUNNING");
            result.addCheckStep(dbConnectStep);

            boolean dbConnected = databaseService.testConnection(dbConfig);
            dbConnectStep.setSuccess(dbConnected);
            dbConnectStep.setResult(dbConnected ? "数据库连接成功" : "数据库连接失败: " + dbConfig.getErrorMessage());

            if (!dbConnected) {
                return false;
            }

            // 步骤4：执行SQL查询（如果有SQL语句）
            if (checkItem.getCommand() != null && !checkItem.getCommand().trim().isEmpty()) {
                CheckStep queryStep = new CheckStep("执行SQL查询", "执行数据库查询", "DB_QUERY");
                queryStep.setStatus("RUNNING");
                result.addCheckStep(queryStep);

                queryStep.setCommand(checkItem.getCommand());
                String queryResult = databaseService.executeQuery(dbConfig, checkItem.getCommand());

                queryStep.setSuccess(true);
                queryStep.setResult(queryResult);
                result.setActualResult(queryResult);
            } else {
                result.setActualResult("数据库连接测试成功");
            }

            return true;

        } catch (Exception e) {
            connectStep.setSuccess(false);
            connectStep.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 从服务器读取数据库配置
     */
    private DatabaseConfig readDatabaseConfig(ServerInfo serverInfo) {
        try {
            String envContent = sshService.executeCommand(serverInfo, "cat /etc/CET/docker/.env");

            DatabaseConfig config = new DatabaseConfig();
            String[] lines = envContent.split("\n");

            for (String line : lines) {
                line = line.trim();
                if (line.startsWith("DataBaseUrl=")) {
                    config.setUrl(line.substring("DataBaseUrl=".length()));
                } else if (line.startsWith("DataBasePort=")) {
                    config.setPort(line.substring("DataBasePort=".length()));
                } else if (line.startsWith("DataBaseUser=")) {
                    config.setUsername(line.substring("DataBaseUser=".length()));
                } else if (line.startsWith("DataBasePwd=")) {
                    config.setPassword(line.substring("DataBasePwd=".length()));
                } else if (line.startsWith("DBProjectName=")) {
                    config.setProjectName(line.substring("DBProjectName=".length()));
                }
            }

            // 设置默认数据库名
            config.setDatabase("postgres");

            return config;

        } catch (Exception e) {
            logger.error("读取数据库配置失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 执行文件存在检查
     */
    private boolean executeFileExistsCheck(ServerInfo serverInfo, CheckItem checkItem, CheckResult result) {
        // 步骤1：连接服务器
        CheckStep connectStep = new CheckStep("连接服务器", "建立SSH连接", "SSH_CONNECT");
        connectStep.setStatus("RUNNING");
        result.addCheckStep(connectStep);

        try {
            boolean connected = sshService.testConnection(serverInfo);
            connectStep.setSuccess(connected);
            connectStep.setResult(connected ? "连接成功" : "连接失败");

            if (!connected) {
                return false;
            }

            // 步骤2：检查文件
            CheckStep fileStep = new CheckStep("检查文件", "检查文件是否存在", "SSH_COMMAND");
            fileStep.setCommand("test -f " + checkItem.getCommand() + " && echo 'exists' || echo 'not exists'");
            fileStep.setStatus("RUNNING");
            result.addCheckStep(fileStep);

            boolean exists = sshService.checkFileExists(serverInfo, checkItem.getCommand());
            fileStep.setSuccess(true);
            fileStep.setResult(exists ? "文件存在" : "文件不存在");
            result.setActualResult(exists ? "文件存在" : "文件不存在");

            return exists;

        } catch (Exception e) {
            connectStep.setSuccess(false);
            connectStep.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 执行进程检查
     */
    private boolean executeProcessCheck(ServerInfo serverInfo, CheckItem checkItem, CheckResult result) {
        // 步骤1：连接服务器
        CheckStep connectStep = new CheckStep("连接服务器", "建立SSH连接", "SSH_CONNECT");
        connectStep.setStatus("RUNNING");
        result.addCheckStep(connectStep);

        try {
            boolean connected = sshService.testConnection(serverInfo);
            connectStep.setSuccess(connected);
            connectStep.setResult(connected ? "连接成功" : "连接失败");

            if (!connected) {
                return false;
            }

            // 步骤2：检查进程
            CheckStep processStep = new CheckStep("检查进程", "检查进程是否运行", "SSH_COMMAND");
            processStep.setCommand("pgrep -f " + checkItem.getCommand());
            processStep.setStatus("RUNNING");
            result.addCheckStep(processStep);

            boolean running = sshService.checkProcessRunning(serverInfo, checkItem.getCommand());
            processStep.setSuccess(true);
            processStep.setResult(running ? "进程运行中" : "进程未运行");
            result.setActualResult(running ? "进程运行中" : "进程未运行");

            return running;

        } catch (Exception e) {
            connectStep.setSuccess(false);
            connectStep.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 执行端口检查
     */
    private boolean executePortCheck(ServerInfo serverInfo, CheckItem checkItem, CheckResult result) {
        // 步骤1：连接服务器
        CheckStep connectStep = new CheckStep("连接服务器", "建立SSH连接", "SSH_CONNECT");
        connectStep.setStatus("RUNNING");
        result.addCheckStep(connectStep);

        try {
            boolean connected = sshService.testConnection(serverInfo);
            connectStep.setSuccess(connected);
            connectStep.setResult(connected ? "连接成功" : "连接失败");

            if (!connected) {
                return false;
            }

            // 步骤2：检查端口
            CheckStep portStep = new CheckStep("检查端口", "检查端口是否监听", "SSH_COMMAND");
            portStep.setCommand("netstat -tlnp | grep :" + checkItem.getCommand());
            portStep.setStatus("RUNNING");
            result.addCheckStep(portStep);

            try {
                int port = Integer.parseInt(checkItem.getCommand());
                boolean listening = sshService.checkPortListening(serverInfo, port);
                portStep.setSuccess(true);
                portStep.setResult(listening ? "端口监听中" : "端口未监听");
                result.setActualResult(listening ? "端口监听中" : "端口未监听");

                return listening;

            } catch (NumberFormatException e) {
                portStep.setSuccess(false);
                portStep.setErrorMessage("端口号格式错误");
                result.setActualResult("端口号格式错误");
                return false;
            }

        } catch (Exception e) {
            connectStep.setSuccess(false);
            connectStep.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 使用完整请求执行数据库检查（支持连接复用）
     */
    private boolean executeDatabaseCheckWithRequest(CheckExecutionRequest request, CheckItem checkItem,
                                                   CheckResult result, Map<String, Boolean> serverConnections,
                                                   Map<String, Boolean> databaseConnections) {
        // 对于直接数据库检查项，直接连接数据库
        if ("db_alarm_count".equals(checkItem.getId())) {
            return executeAlarmCountCheckWithRequest(request, result, databaseConnections);
        } else if ("pecdeviceextend_duplicate".equals(checkItem.getId())) {
            return executePecdeviceextendDuplicateCheck(request, result, databaseConnections);
        } else if ("alarm_point_underscore".equals(checkItem.getId())) {
            return executeAlarmPointUnderscoreCheck(request, result, databaseConnections);
        }

        // 其他数据库检查需要先连接服务器读取配置
        // 步骤1：连接服务器（使用连接缓存）
        CheckStep connectStep = new CheckStep("连接服务器", "建立SSH连接", "SSH_CONNECT");
        connectStep.setStatus("RUNNING");
        result.addCheckStep(connectStep);

        try {
            ServerInfo serverInfo = request.getVipServer();
            String serverKey = serverInfo.getHost() + ":" + serverInfo.getPort();

            boolean connected;
            if (serverConnections.containsKey(serverKey)) {
                connected = serverConnections.get(serverKey);
                connectStep.setResult("使用已有连接");
            } else {
                connected = sshService.testConnection(serverInfo);
                serverConnections.put(serverKey, connected);
                connectStep.setResult(connected ? "连接成功" : "连接失败");
            }

            connectStep.setSuccess(connected);

            if (!connected) {
                return false;
            }

            // 步骤2：读取数据库配置
            CheckStep configStep = new CheckStep("读取数据库配置", "从.env文件读取数据库配置", "SSH_COMMAND");
            configStep.setCommand("cat /etc/CET/docker/.env | grep -E 'DataBase(Url|Port|User|Pwd)'");
            configStep.setStatus("RUNNING");
            result.addCheckStep(configStep);

            DatabaseConfig dbConfig = readDatabaseConfig(serverInfo);
            if (dbConfig == null) {
                configStep.setSuccess(false);
                configStep.setErrorMessage("无法读取数据库配置");
                return false;
            }

            configStep.setSuccess(true);
            configStep.setResult("数据库配置读取成功: " + dbConfig.getUrl() + ":" + dbConfig.getPort());

            // 步骤3：连接数据库（使用连接缓存）
            CheckStep dbConnectStep = new CheckStep("连接数据库", "建立PostgreSQL数据库连接", "DB_CONNECT");
            dbConnectStep.setStatus("RUNNING");
            result.addCheckStep(dbConnectStep);

            String dbKey = dbConfig.getUrl() + ":" + dbConfig.getPort() + ":" + dbConfig.getUsername();
            boolean dbConnected;
            if (databaseConnections.containsKey(dbKey)) {
                dbConnected = databaseConnections.get(dbKey);
                dbConnectStep.setResult("使用已有数据库连接");
            } else {
                dbConnected = databaseService.testConnection(dbConfig);
                databaseConnections.put(dbKey, dbConnected);
                dbConnectStep.setResult(dbConnected ? "数据库连接成功" : "数据库连接失败: " + dbConfig.getErrorMessage());
            }

            dbConnectStep.setSuccess(dbConnected);

            if (!dbConnected) {
                return false;
            }

            // 步骤4：执行SQL查询
            if (checkItem.getCommand() != null && !checkItem.getCommand().trim().isEmpty()) {
                CheckStep queryStep = new CheckStep("执行SQL查询", "执行数据库查询", "DB_QUERY");
                queryStep.setStatus("RUNNING");
                result.addCheckStep(queryStep);

                queryStep.setCommand(checkItem.getCommand());
                String queryResult = databaseService.executeQuery(dbConfig, checkItem.getCommand());

                queryStep.setSuccess(true);
                queryStep.setResult(queryResult);
                result.setActualResult(queryResult);
            } else {
                result.setActualResult("数据库连接测试成功");
            }

            return true;

        } catch (Exception e) {
            connectStep.setSuccess(false);
            connectStep.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 使用完整请求执行告警统计检查
     */
    private boolean executeAlarmCountCheckWithRequest(CheckExecutionRequest request, CheckResult result,
                                                     Map<String, Boolean> databaseConnections) {
        // 步骤1：获取数据库配置
        CheckStep configStep = new CheckStep("获取数据库配置", "从请求中获取数据库连接信息", "DB_CONNECT");
        configStep.setStatus("RUNNING");
        result.addCheckStep(configStep);

        try {
            DatabaseConfig dbConfig = request.getDatabaseConfig();
            if (dbConfig == null) {
                configStep.setSuccess(false);
                configStep.setErrorMessage("无法获取数据库配置");
                return false;
            }

            configStep.setSuccess(true);
            configStep.setResult("数据库配置获取成功: " + dbConfig.getUrl() + ":" + dbConfig.getPort());

            // 步骤2：连接数据库（使用连接缓存）
            CheckStep dbConnectStep = new CheckStep("连接数据库", "建立PostgreSQL数据库连接", "DB_CONNECT");
            dbConnectStep.setStatus("RUNNING");
            result.addCheckStep(dbConnectStep);

            String dbKey = dbConfig.getUrl() + ":" + dbConfig.getPort() + ":" + dbConfig.getUsername();
            boolean dbConnected;
            if (databaseConnections.containsKey(dbKey)) {
                dbConnected = databaseConnections.get(dbKey);
                dbConnectStep.setResult("使用已有数据库连接");
            } else {
                dbConnected = databaseService.testConnection(dbConfig);
                databaseConnections.put(dbKey, dbConnected);
                dbConnectStep.setResult(dbConnected ? "数据库连接成功" : "数据库连接失败: " + dbConfig.getErrorMessage());
            }

            dbConnectStep.setSuccess(dbConnected);

            if (!dbConnected) {
                return false;
            }

            // 步骤3：执行告警统计查询
            CheckStep queryStep = new CheckStep("执行告警统计", "查询当月告警数量", "DB_QUERY");
            queryStep.setStatus("RUNNING");
            result.addCheckStep(queryStep);

            String queryResult = executeAlarmCountCheck(dbConfig, queryStep);
            queryStep.setSuccess(true);
            queryStep.setResult(queryResult);
            result.setActualResult(queryResult);

            return true;

        } catch (Exception e) {
            configStep.setSuccess(false);
            configStep.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 直接执行告警统计检查（不需要连接服务器）
     */
    private boolean executeAlarmCountCheckDirect(CheckResult result) {
        // 步骤1：获取数据库配置
        CheckStep configStep = new CheckStep("获取数据库配置", "从前端配置获取数据库连接信息", "DB_CONNECT");
        configStep.setStatus("RUNNING");
        result.addCheckStep(configStep);

        try {
            // 这里应该从前端传入的数据库配置中获取，暂时使用默认配置
            // TODO: 需要从前端获取数据库配置
            DatabaseConfig dbConfig = getDefaultDatabaseConfig();
            if (dbConfig == null) {
                configStep.setSuccess(false);
                configStep.setErrorMessage("无法获取数据库配置");
                return false;
            }

            configStep.setSuccess(true);
            configStep.setResult("数据库配置获取成功: " + dbConfig.getUrl() + ":" + dbConfig.getPort());

            // 步骤2：连接数据库
            CheckStep dbConnectStep = new CheckStep("连接数据库", "建立PostgreSQL数据库连接", "DB_CONNECT");
            dbConnectStep.setStatus("RUNNING");
            result.addCheckStep(dbConnectStep);

            boolean dbConnected = databaseService.testConnection(dbConfig);
            dbConnectStep.setSuccess(dbConnected);
            dbConnectStep.setResult(dbConnected ? "数据库连接成功" : "数据库连接失败: " + dbConfig.getErrorMessage());

            if (!dbConnected) {
                return false;
            }

            // 步骤3：执行告警统计查询
            CheckStep queryStep = new CheckStep("执行告警统计", "查询当月告警数量", "DB_QUERY");
            queryStep.setStatus("RUNNING");
            result.addCheckStep(queryStep);

            String queryResult = executeAlarmCountCheck(dbConfig, queryStep);
            queryStep.setSuccess(true);
            queryStep.setResult(queryResult);
            result.setActualResult(queryResult);

            return true;

        } catch (Exception e) {
            configStep.setSuccess(false);
            configStep.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 获取默认数据库配置（临时方法）
     */
    private DatabaseConfig getDefaultDatabaseConfig() {
        // TODO: 这里应该从前端传入的配置中获取
        // 暂时返回null，需要后续完善
        return null;
    }

    /**
     * 执行告警统计检查
     */
    private String executeAlarmCountCheck(DatabaseConfig dbConfig, CheckStep queryStep) {
        try {
            // 构建Matterhorn数据库名
            String matterhornDb = "Matterhorn_" + (dbConfig.getProjectName() != null ? dbConfig.getProjectName() : "default");

            // 创建新的数据库配置，指向Matterhorn数据库
            DatabaseConfig matterhornConfig = new DatabaseConfig();
            matterhornConfig.setUrl(dbConfig.getUrl());
            matterhornConfig.setPort(dbConfig.getPort());
            matterhornConfig.setUsername(dbConfig.getUsername());
            matterhornConfig.setPassword(dbConfig.getPassword());
            matterhornConfig.setDatabase(matterhornDb);

            // 获取当月时间范围（毫秒时间戳）
            java.time.LocalDate now = java.time.LocalDate.now();
            java.time.LocalDate firstDayOfMonth = now.withDayOfMonth(1);
            java.time.LocalDate firstDayOfNextMonth = firstDayOfMonth.plusMonths(1);

            long startTime = firstDayOfMonth.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
            long endTime = firstDayOfNextMonth.atStartOfDay(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();

            // 构建SQL查询
            String sql = "SELECT COUNT(*) as alarm_count FROM pecalarmextend WHERE eventtime >= " + startTime + " AND eventtime < " + endTime;
            queryStep.setCommand(sql);

            // 执行查询
            String queryResult = databaseService.executeQuery(matterhornConfig, sql);

            // 解析结果，检查是否超过300万
            try {
                String[] lines = queryResult.split("\n");
                if (lines.length >= 3) { // 跳过标题行和分隔线
                    String dataLine = lines[2].trim();
                    long alarmCount = Long.parseLong(dataLine);

                    String result = queryResult + "\n\n";
                    if (alarmCount > 3000000) {
                        result += "⚠️ 警告：当月告警数量 " + alarmCount + " 已超过300万！";
                    } else {
                        result += "✅ 正常：当月告警数量 " + alarmCount + "，未超过300万";
                    }

                    return result;
                }
            } catch (Exception e) {
                logger.warn("解析告警统计结果失败: {}", e.getMessage());
            }

            return queryResult + "\n\n无法解析告警统计结果";

        } catch (Exception e) {
            logger.error("执行告警统计检查失败: {}", e.getMessage());
            return "执行告警统计检查失败: " + e.getMessage();
        }
    }

    /**
     * 使用连接复用执行命令检查
     */
    private boolean executeCommandCheckWithRequest(ServerInfo serverInfo, CheckItem checkItem,
                                                  CheckResult result, Map<String, Boolean> serverConnections) {
        // 步骤1：连接服务器（使用连接缓存）
        CheckStep connectStep = new CheckStep("连接服务器", "建立SSH连接", "SSH_CONNECT");
        connectStep.setStatus("RUNNING");
        result.addCheckStep(connectStep);

        try {
            String serverKey = serverInfo.getHost() + ":" + serverInfo.getPort();
            boolean connected;

            if (serverConnections.containsKey(serverKey)) {
                connected = serverConnections.get(serverKey);
                connectStep.setResult("使用已有连接");
            } else {
                connected = sshService.testConnection(serverInfo);
                serverConnections.put(serverKey, connected);
                connectStep.setResult(connected ? "连接成功" : "连接失败");
            }

            connectStep.setSuccess(connected);

            if (!connected) {
                return false;
            }

            // 步骤2：执行命令
            CheckStep commandStep = new CheckStep("执行命令", "执行系统命令", "SSH_COMMAND");
            commandStep.setCommand(checkItem.getCommand());
            commandStep.setStatus("RUNNING");
            result.addCheckStep(commandStep);

            String commandResult = sshService.executeCommand(serverInfo, checkItem.getCommand());
            commandStep.setSuccess(true);
            commandStep.setResult(commandResult);

            // 步骤3：分析结果
            CheckStep analysisStep = new CheckStep("分析结果", "分析命令执行结果", "ANALYSIS");
            analysisStep.setStatus("RUNNING");
            result.addCheckStep(analysisStep);

            analysisStep.setSuccess(true);
            analysisStep.setResult("命令执行成功");
            result.setActualResult(commandResult);

            return true;

        } catch (Exception e) {
            connectStep.setSuccess(false);
            connectStep.setErrorMessage(e.getMessage());
            return false;
        }
    }

    /**
     * 使用连接复用执行文件存在检查
     */
    private boolean executeFileExistsCheckWithRequest(ServerInfo serverInfo, CheckItem checkItem,
                                                     CheckResult result, Map<String, Boolean> serverConnections) {
        // 复用连接逻辑与命令检查类似
        return executeCommandCheckWithRequest(serverInfo, checkItem, result, serverConnections);
    }

    /**
     * 使用连接复用执行进程检查
     */
    private boolean executeProcessCheckWithRequest(ServerInfo serverInfo, CheckItem checkItem,
                                                  CheckResult result, Map<String, Boolean> serverConnections) {
        // 复用连接逻辑与命令检查类似
        return executeCommandCheckWithRequest(serverInfo, checkItem, result, serverConnections);
    }

    /**
     * 使用连接复用执行端口检查
     */
    private boolean executePortCheckWithRequest(ServerInfo serverInfo, CheckItem checkItem,
                                               CheckResult result, Map<String, Boolean> serverConnections) {
        // 复用连接逻辑与命令检查类似
        return executeCommandCheckWithRequest(serverInfo, checkItem, result, serverConnections);
    }

    /**
     * 执行多服务器检查
     */
    private boolean executeMultiServerCheck(CheckExecutionRequest request, CheckItem checkItem,
                                           CheckResult result, Map<String, Boolean> serverConnections) {
        try {
            switch (checkItem.getId()) {
                case "docker_version_business":
                    return checkDockerVersionConsistency(request, result, serverConnections,
                            request.getMainServer(), request.getBackupServer(), "业务主服务器", "业务备服务器");

                case "docker_version_collect":
                    return checkDockerVersionConsistency(request, result, serverConnections,
                            request.getCollectMainServer(), request.getCollectBackupServer(), "采集主服务器", "采集备服务器");

                case "device_data_service_version":
                    return checkDeviceDataServiceVersion(request, result, serverConnections);

                case "server_time_diff_business":
                    return checkServerTimeDifference(request, result, serverConnections,
                            request.getMainServer(), request.getBackupServer(), "业务主服务器", "业务备服务器");

                case "server_time_diff_collect":
                    return checkServerTimeDifference(request, result, serverConnections,
                            request.getCollectMainServer(), request.getCollectBackupServer(), "采集主服务器", "采集备服务器");

                case "backup_service_status":
                    return checkBackupServiceStatus(request, result, serverConnections);

                case "server_resource_usage":
                    return checkServerResourceUsage(request, result, serverConnections);

                case "service_log_exception":
                    return checkServiceLogExceptions(request, result, serverConnections);

                case "ssh_passwordless_business":
                    return checkSSHPasswordless(request, result, serverConnections);

                case "file_sync_business":
                    return checkFileSync(request, result, serverConnections);

                case "netclient_version_check":
                    return checkNetclientVersion(request, result, serverConnections);

                default:
                    CheckStep errorStep = new CheckStep("错误", "不支持的多服务器检查项", "ERROR");
                    errorStep.setSuccess(false);
                    errorStep.setErrorMessage("不支持的多服务器检查项: " + checkItem.getId());
                    result.addCheckStep(errorStep);
                    return false;
            }
        } catch (Exception e) {
            CheckStep errorStep = new CheckStep("异常", "多服务器检查异常", "ERROR");
            errorStep.setSuccess(false);
            errorStep.setErrorMessage(e.getMessage());
            result.addCheckStep(errorStep);
            return false;
        }
    }

    /**
     * 执行配置检查
     */
    private boolean executeConfigCheck(CheckExecutionRequest request, CheckItem checkItem, CheckResult result) {
        try {
            switch (checkItem.getId()) {
                case "project_name_check":
                    return checkProjectNameFormat(request, result);

                case "alarm_auth_push_check":
                    return checkAlarmAuthPush(request, result);

                case "alarm_subscription_check":
                    return checkAlarmSubscription(request, result);

                case "onlyreport_config_check":
                    return checkOnlyReportConfig(request, result);

                case "docker_compose_tab_check":
                    return checkDockerComposeTabCharacters(request, result);

                default:
                    CheckStep errorStep = new CheckStep("错误", "不支持的配置检查项", "ERROR");
                    errorStep.setSuccess(false);
                    errorStep.setErrorMessage("不支持的配置检查项: " + checkItem.getId());
                    result.addCheckStep(errorStep);
                    return false;
            }
        } catch (Exception e) {
            CheckStep errorStep = new CheckStep("异常", "配置检查异常", "ERROR");
            errorStep.setSuccess(false);
            errorStep.setErrorMessage(e.getMessage());
            result.addCheckStep(errorStep);
            return false;
        }
    }

    /**
     * 根据ID查找检查项
     */
    private CheckItem findCheckItemById(String id) {
        return checkItems.stream()
                .filter(item -> id.equals(item.getId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 检查Docker版本一致性
     */
    private boolean checkDockerVersionConsistency(CheckExecutionRequest request, CheckResult result,
                                                 Map<String, Boolean> serverConnections,
                                                 ServerInfo server1, ServerInfo server2,
                                                 String server1Name, String server2Name) {
        // 步骤1：连接第一台服务器
        CheckStep connect1Step = new CheckStep("连接" + server1Name, "建立SSH连接", "SSH_CONNECT");
        connect1Step.setStatus("RUNNING");
        result.addCheckStep(connect1Step);

        String server1Key = server1.getHost() + ":" + server1.getPort();
        boolean connected1;
        if (serverConnections.containsKey(server1Key)) {
            connected1 = serverConnections.get(server1Key);
            connect1Step.setResult("使用已有连接");
        } else {
            connected1 = sshService.testConnection(server1);
            serverConnections.put(server1Key, connected1);
            connect1Step.setResult(connected1 ? "连接成功" : "连接失败");
        }
        connect1Step.setSuccess(connected1);

        if (!connected1) {
            return false;
        }

        // 步骤2：检查第一台服务器Docker容器版本
        CheckStep docker1Step = new CheckStep("检查" + server1Name + "Docker容器版本", "获取运行中的Docker容器版本信息", "SSH_COMMAND");
        docker1Step.setCommand("docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort");
        docker1Step.setStatus("RUNNING");
        result.addCheckStep(docker1Step);

        String docker1Containers = sshService.executeCommand(server1, "docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort");
        docker1Step.setSuccess(true);
        docker1Step.setResult(docker1Containers.trim().isEmpty() ? "无运行中的容器" : docker1Containers.trim());

        // 步骤3：连接第二台服务器
        CheckStep connect2Step = new CheckStep("连接" + server2Name, "建立SSH连接", "SSH_CONNECT");
        connect2Step.setStatus("RUNNING");
        result.addCheckStep(connect2Step);

        String server2Key = server2.getHost() + ":" + server2.getPort();
        boolean connected2;
        if (serverConnections.containsKey(server2Key)) {
            connected2 = serverConnections.get(server2Key);
            connect2Step.setResult("使用已有连接");
        } else {
            connected2 = sshService.testConnection(server2);
            serverConnections.put(server2Key, connected2);
            connect2Step.setResult(connected2 ? "连接成功" : "连接失败");
        }
        connect2Step.setSuccess(connected2);

        if (!connected2) {
            result.setActualResult("❌ " + server2Name + "连接失败，无法比较Docker容器版本");
            return false;
        }

        // 步骤4：检查第二台服务器Docker容器版本
        CheckStep docker2Step = new CheckStep("检查" + server2Name + "Docker容器版本", "获取运行中的Docker容器版本信息", "SSH_COMMAND");
        docker2Step.setCommand("docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort");
        docker2Step.setStatus("RUNNING");
        result.addCheckStep(docker2Step);

        String docker2Containers = sshService.executeCommand(server2, "docker ps --format 'table {{.Image}}' | grep -v IMAGE | sort");
        docker2Step.setSuccess(true);
        docker2Step.setResult(docker2Containers.trim().isEmpty() ? "无运行中的容器" : docker2Containers.trim());

        // 步骤5：比较容器版本
        CheckStep compareStep = new CheckStep("容器版本比较", "比较两台服务器运行的Docker容器版本", "ANALYSIS");
        compareStep.setStatus("RUNNING");
        result.addCheckStep(compareStep);

        String compareResult = compareDockerContainers(docker1Containers, docker2Containers, server1Name, server2Name);
        boolean versionsMatch = !compareResult.contains("❌");

        compareStep.setSuccess(versionsMatch);
        compareStep.setResult(compareResult);
        result.setActualResult(compareResult);

        return versionsMatch;
    }

    /**
     * 比较Docker容器版本，只显示不一致的程序
     */
    private String compareDockerContainers(String containers1, String containers2, String server1Name, String server2Name) {
        if (containers1.trim().isEmpty() && containers2.trim().isEmpty()) {
            return "⚠️ 两台服务器都没有运行中的Docker容器";
        } else if (containers1.trim().isEmpty()) {
            return "❌ " + server1Name + "没有运行中的容器，" + server2Name + "运行的容器:\n" + containers2.trim();
        } else if (containers2.trim().isEmpty()) {
            return "❌ " + server2Name + "没有运行中的容器，" + server1Name + "运行的容器:\n" + containers1.trim();
        }

        // 解析容器列表
        String[] list1 = containers1.trim().split("\n");
        String[] list2 = containers2.trim().split("\n");

        Set<String> set1 = new HashSet<>();
        Set<String> set2 = new HashSet<>();

        for (String container : list1) {
            if (!container.trim().isEmpty()) {
                set1.add(container.trim());
            }
        }

        for (String container : list2) {
            if (!container.trim().isEmpty()) {
                set2.add(container.trim());
            }
        }

        // 找出不一致的容器
        Set<String> onlyInServer1 = new HashSet<>(set1);
        onlyInServer1.removeAll(set2);

        Set<String> onlyInServer2 = new HashSet<>(set2);
        onlyInServer2.removeAll(set1);

        if (onlyInServer1.isEmpty() && onlyInServer2.isEmpty()) {
            return "✅ 两台服务器运行的Docker容器版本完全一致";
        }

        StringBuilder result = new StringBuilder();
        result.append("❌ 发现版本不一致的Docker程序:\n");

        if (!onlyInServer1.isEmpty()) {
            result.append("\n").append(server1Name).append("独有的程序版本:\n");
            for (String container : onlyInServer1) {
                result.append("  - ").append(container).append("\n");
            }
        }

        if (!onlyInServer2.isEmpty()) {
            result.append("\n").append(server2Name).append("独有的程序版本:\n");
            for (String container : onlyInServer2) {
                result.append("  - ").append(container).append("\n");
            }
        }

        return result.toString();
    }

    /**
     * 检查设备数据服务版本
     */
    private boolean checkDeviceDataServiceVersion(CheckExecutionRequest request, CheckResult result,
                                                 Map<String, Boolean> serverConnections) {
        String targetVersion = "v2.7.29.8";
        boolean allVersionsOk = true;
        StringBuilder resultBuilder = new StringBuilder();

        // 检查业务主服务器
        allVersionsOk &= checkSingleServerDeviceDataService(request.getMainServer(), "业务主服务器",
                targetVersion, result, serverConnections, resultBuilder);

        // 检查业务备服务器
        allVersionsOk &= checkSingleServerDeviceDataService(request.getBackupServer(), "业务备服务器",
                targetVersion, result, serverConnections, resultBuilder);

        // 设置最终结果
        result.setActualResult(resultBuilder.toString());
        return allVersionsOk;
    }

    /**
     * 检查单台服务器的设备数据服务版本
     */
    private boolean checkSingleServerDeviceDataService(ServerInfo server, String serverName, String targetVersion,
                                                      CheckResult result, Map<String, Boolean> serverConnections,
                                                      StringBuilder resultBuilder) {
        // 连接服务器
        CheckStep connectStep = new CheckStep("连接" + serverName, "建立SSH连接", "SSH_CONNECT");
        connectStep.setStatus("RUNNING");
        result.addCheckStep(connectStep);

        String serverKey = server.getHost() + ":" + server.getPort();
        boolean connected;
        if (serverConnections.containsKey(serverKey)) {
            connected = serverConnections.get(serverKey);
            connectStep.setResult("使用已有连接");
        } else {
            connected = sshService.testConnection(server);
            serverConnections.put(serverKey, connected);
            connectStep.setResult(connected ? "连接成功" : "连接失败");
        }
        connectStep.setSuccess(connected);

        if (!connected) {
            resultBuilder.append(serverName).append(": 连接失败\n");
            return false;
        }

        // 检查运行中的设备数据服务版本
        CheckStep versionStep = new CheckStep("检查" + serverName + "设备数据服务版本", "获取运行中的device-data-service版本", "SSH_COMMAND");
        versionStep.setCommand("docker ps --format 'table {{.Image}}' | grep device-data-service");
        versionStep.setStatus("RUNNING");
        result.addCheckStep(versionStep);

        String runningContainer = sshService.executeCommand(server, "docker ps --format 'table {{.Image}}' | grep device-data-service");
        runningContainer = runningContainer.trim();

        String currentVersion = "";
        if (!runningContainer.isEmpty()) {
            // 从容器镜像名中提取版本号
            String[] parts = runningContainer.split(":");
            if (parts.length > 1) {
                currentVersion = parts[1].trim();
            }
        }

        versionStep.setSuccess(true);
        versionStep.setResult(runningContainer.isEmpty() ? "device-data-service服务未运行" : "运行版本: " + currentVersion);

        boolean versionOk = true;
        if (runningContainer.isEmpty()) {
            resultBuilder.append(serverName).append(": ❌ device-data-service服务未运行，请检查服务状态\n");
            versionOk = false;
        } else if (currentVersion.isEmpty()) {
            resultBuilder.append(serverName).append(": ❌ 无法获取device-data-service版本信息\n");
            versionOk = false;
        } else {
            // 版本比较
            if (compareVersions(currentVersion, targetVersion) < 0) {
                resultBuilder.append(serverName).append(": ❌ 设备数据服务版本过低 ").append(currentVersion)
                           .append("，需要更新到 ").append(targetVersion).append(" 或更高版本\n");
                versionOk = false;
            } else {
                resultBuilder.append(serverName).append(": ✅ 设备数据服务版本正常 ").append(currentVersion).append("\n");
            }
        }

        return versionOk;
    }

    /**
     * 简单的版本比较方法
     */
    private int compareVersions(String version1, String version2) {
        try {
            // 移除v前缀
            String v1 = version1.startsWith("v") ? version1.substring(1) : version1;
            String v2 = version2.startsWith("v") ? version2.substring(1) : version2;

            String[] parts1 = v1.split("\\.");
            String[] parts2 = v2.split("\\.");

            int maxLength = Math.max(parts1.length, parts2.length);
            for (int i = 0; i < maxLength; i++) {
                int num1 = i < parts1.length ? Integer.parseInt(parts1[i]) : 0;
                int num2 = i < parts2.length ? Integer.parseInt(parts2[i]) : 0;

                if (num1 != num2) {
                    return Integer.compare(num1, num2);
                }
            }
            return 0;
        } catch (Exception e) {
            // 如果版本格式不标准，按字符串比较
            return version1.compareTo(version2);
        }
    }

    /**
     * 检查服务器时间差
     */
    private boolean checkServerTimeDifference(CheckExecutionRequest request, CheckResult result,
                                             Map<String, Boolean> serverConnections,
                                             ServerInfo server1, ServerInfo server2,
                                             String server1Name, String server2Name) {
        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 获取第一台服务器时间
        CheckStep time1Step = new CheckStep("获取" + server1Name + "时间", "获取服务器时间戳", "SSH_COMMAND");
        time1Step.setCommand("date +%s");
        time1Step.setStatus("RUNNING");
        result.addCheckStep(time1Step);

        String server1Key = server1.getHost() + ":" + server1.getPort();
        if (!serverConnections.getOrDefault(server1Key, false)) {
            boolean connected = sshService.testConnection(server1);
            serverConnections.put(server1Key, connected);
            if (!connected) {
                time1Step.setSuccess(false);
                time1Step.setErrorMessage("服务器连接失败，无法获取时间");
                result.setActualResult("❌ " + server1Name + "连接失败，无法进行时间比较");
                return false;
            }
        }

        String time1Str = sshService.executeCommand(server1, "date +%s");
        long time1 = Long.parseLong(time1Str.trim());
        long time1GetTime = System.currentTimeMillis();
        time1Step.setSuccess(true);
        time1Step.setResult("时间戳: " + time1);

        // 获取第二台服务器时间
        CheckStep time2Step = new CheckStep("获取" + server2Name + "时间", "获取服务器时间戳", "SSH_COMMAND");
        time2Step.setCommand("date +%s");
        time2Step.setStatus("RUNNING");
        result.addCheckStep(time2Step);

        String server2Key = server2.getHost() + ":" + server2.getPort();
        if (!serverConnections.getOrDefault(server2Key, false)) {
            boolean connected = sshService.testConnection(server2);
            serverConnections.put(server2Key, connected);
            if (!connected) {
                time2Step.setSuccess(false);
                time2Step.setErrorMessage("服务器连接失败，无法获取时间");
                result.setActualResult("❌ " + server2Name + "连接失败，无法进行时间比较");
                return false;
            }
        }

        String time2Str = sshService.executeCommand(server2, "date +%s");
        long time2 = Long.parseLong(time2Str.trim());
        long time2GetTime = System.currentTimeMillis();
        time2Step.setSuccess(true);
        time2Step.setResult("时间戳: " + time2);

        // 比较时间差，考虑网络延迟
        CheckStep compareStep = new CheckStep("时间差比较", "比较两台服务器时间差（已补偿网络延迟）", "ANALYSIS");
        compareStep.setStatus("RUNNING");
        result.addCheckStep(compareStep);

        // 计算网络延迟补偿
        long networkDelay = (time2GetTime - time1GetTime) / 1000; // 转换为秒
        long adjustedTime1 = time1 + networkDelay; // 将第一台服务器时间向后调整

        long timeDiff = Math.abs(adjustedTime1 - time2);
        boolean timeOk = timeDiff <= 10;

        String compareResult;
        if (timeOk) {
            compareResult = "✅ 服务器时间同步正常，时间差: " + timeDiff + " 秒（已补偿网络延迟 " + networkDelay + " 秒）";
        } else {
            compareResult = "❌ 服务器时间差过大: " + timeDiff + " 秒，超过10秒阈值。请检查服务器时间同步配置（NTP服务）";
        }

        compareStep.setSuccess(timeOk);
        compareStep.setResult(compareResult);
        result.setActualResult(compareResult);

        return timeOk;
    }

    /**
     * 检查备份服务状态
     */
    private boolean checkBackupServiceStatus(CheckExecutionRequest request, CheckResult result,
                                           Map<String, Boolean> serverConnections) {
        boolean allServicesOk = true;
        StringBuilder resultBuilder = new StringBuilder();

        // 检查业务主服务器
        allServicesOk &= checkSingleServerBackupService(request.getMainServer(), "业务主服务器",
                result, serverConnections, resultBuilder);

        // 检查业务备服务器
        allServicesOk &= checkSingleServerBackupService(request.getBackupServer(), "业务备服务器",
                result, serverConnections, resultBuilder);

        result.setActualResult(resultBuilder.toString());
        return allServicesOk;
    }

    /**
     * 检查单台服务器的备份服务状态
     */
    private boolean checkSingleServerBackupService(ServerInfo server, String serverName,
                                                  CheckResult result, Map<String, Boolean> serverConnections,
                                                  StringBuilder resultBuilder) {
        CheckStep serviceStep = new CheckStep("检查" + serverName + "备份服务", "检查dbbackup-assistant.service状态", "SSH_COMMAND");
        serviceStep.setCommand("systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive'");
        serviceStep.setStatus("RUNNING");
        result.addCheckStep(serviceStep);

        String serverKey = server.getHost() + ":" + server.getPort();
        if (!serverConnections.getOrDefault(serverKey, false)) {
            boolean connected = sshService.testConnection(server);
            serverConnections.put(serverKey, connected);
            if (!connected) {
                serviceStep.setSuccess(false);
                serviceStep.setErrorMessage("连接失败");
                resultBuilder.append(serverName).append(": 连接失败\n");
                return false;
            }
        }

        String serviceStatus = sshService.executeCommand(server, "systemctl is-active dbbackup-assistant.service 2>/dev/null || echo 'inactive'");
        serviceStatus = serviceStatus.trim();

        boolean serviceOk = "active".equals(serviceStatus);
        serviceStep.setSuccess(true);
        serviceStep.setResult("服务状态: " + serviceStatus);

        if (serviceOk) {
            resultBuilder.append(serverName).append(": ✅ 备份服务运行正常\n");
        } else {
            resultBuilder.append(serverName).append(": ❌ 备份服务未运行，状态: ").append(serviceStatus).append("\n");
        }

        return serviceOk;
    }

    /**
     * 检查服务器资源使用率
     */
    private boolean checkServerResourceUsage(CheckExecutionRequest request, CheckResult result,
                                           Map<String, Boolean> serverConnections) {
        boolean allResourcesOk = true;
        StringBuilder resultBuilder = new StringBuilder();

        // 检查所有服务器
        allResourcesOk &= checkSingleServerResourceUsage(request.getVipServer(), "VIP服务器",
                result, serverConnections, resultBuilder);
        allResourcesOk &= checkSingleServerResourceUsage(request.getMainServer(), "业务主服务器",
                result, serverConnections, resultBuilder);
        allResourcesOk &= checkSingleServerResourceUsage(request.getBackupServer(), "业务备服务器",
                result, serverConnections, resultBuilder);
        allResourcesOk &= checkSingleServerResourceUsage(request.getCollectMainServer(), "采集主服务器",
                result, serverConnections, resultBuilder);
        allResourcesOk &= checkSingleServerResourceUsage(request.getCollectBackupServer(), "采集备服务器",
                result, serverConnections, resultBuilder);

        result.setActualResult(resultBuilder.toString());
        return allResourcesOk;
    }

    /**
     * 检查单台服务器的资源使用率
     */
    private boolean checkSingleServerResourceUsage(ServerInfo server, String serverName,
                                                  CheckResult result, Map<String, Boolean> serverConnections,
                                                  StringBuilder resultBuilder) {
        if (server == null || server.getHost() == null) {
            resultBuilder.append(serverName).append(": 配置信息缺失\n");
            return false;
        }

        CheckStep resourceStep = new CheckStep("检查" + serverName + "资源使用率", "检查CPU、内存、磁盘使用率", "SSH_COMMAND");
        // 修改命令：使用与finalshell一致的内存计算方式
        String command = "echo 'CPU:' $(top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1) '%;' && " +
                        "free -h | awk 'NR==2{total=$2; used=$3; available=$7; used_percent=(($2-$7)*100/$2); printf \"Memory:%s/%s(%.1f%%);\",$3,$2,used_percent}' && " +
                        "df -h /var | awk 'NR==2{printf \"Disk:%s/%s(%s);\",$3,$2,$5}'";
        resourceStep.setCommand(command);
        resourceStep.setStatus("RUNNING");
        result.addCheckStep(resourceStep);

        String serverKey = server.getHost() + ":" + server.getPort();
        if (!serverConnections.getOrDefault(serverKey, false)) {
            boolean connected = sshService.testConnection(server);
            serverConnections.put(serverKey, connected);
            if (!connected) {
                resourceStep.setSuccess(false);
                resourceStep.setErrorMessage("服务器连接失败，无法获取资源信息");
                resultBuilder.append(serverName).append(": ❌ 服务器连接失败，无法检查资源使用情况\n");
                return false;
            }
        }

        String resourceInfo = sshService.executeCommand(server, command);
        resourceStep.setSuccess(true);
        resourceStep.setResult(resourceInfo.trim());

        // 解析资源使用率
        boolean resourceOk = true;
        StringBuilder issues = new StringBuilder();
        StringBuilder normalInfo = new StringBuilder();

        try {
            String[] parts = resourceInfo.split(";");
            for (String part : parts) {
                part = part.trim();
                if (part.startsWith("CPU:")) {
                    String cpuStr = part.substring(4).replace("%", "").trim();
                    double cpu = Double.parseDouble(cpuStr);
                    if (cpu > 80) {
                        issues.append("🔴 CPU使用率过高: ").append(String.format("%.1f", cpu)).append("% ");
                        resourceOk = false;
                    } else {
                        normalInfo.append("CPU: ").append(String.format("%.1f", cpu)).append("% ");
                    }
                } else if (part.startsWith("Memory:")) {
                    // 解析内存信息：Memory:已使用/总容量(使用率%)
                    String memInfo = part.substring(7); // 去掉"Memory:"
                    if (memInfo.contains("(") && memInfo.contains(")")) {
                        String usagePercent = memInfo.substring(memInfo.indexOf("(") + 1, memInfo.indexOf(")")).replace("%", "");
                        double memory = Double.parseDouble(usagePercent);
                        if (memory > 80) {
                            issues.append("🔴 内存使用率过高: ").append(memInfo).append(" ");
                            resourceOk = false;
                        } else {
                            normalInfo.append("内存: ").append(memInfo).append(" ");
                        }
                    }
                } else if (part.startsWith("Disk:")) {
                    // 解析磁盘信息：Disk:已使用/总容量(使用率%)
                    String diskInfo = part.substring(5); // 去掉"Disk:"
                    if (diskInfo.contains("(") && diskInfo.contains(")")) {
                        String usagePercent = diskInfo.substring(diskInfo.indexOf("(") + 1, diskInfo.indexOf(")")).replace("%", "");
                        double disk = Double.parseDouble(usagePercent);
                        if (disk > 80) {
                            issues.append("🔴 /var目录磁盘使用率过高: ").append(diskInfo).append(" ");
                            resourceOk = false;
                        } else {
                            normalInfo.append("/var磁盘: ").append(diskInfo).append(" ");
                        }
                    }
                }
            }
        } catch (Exception e) {
            issues.append("❌ 资源信息解析失败，请检查服务器状态 ");
            resourceOk = false;
        }

        if (resourceOk) {
            resultBuilder.append(serverName).append(": ✅ 资源使用率正常 - ").append(normalInfo.toString()).append("\n");
        } else {
            resultBuilder.append(serverName).append(": ").append(issues.toString()).append(normalInfo.toString()).append("\n");
        }

        return resourceOk;
    }

    /**
     * 检查项目名称格式
     */
    private boolean checkProjectNameFormat(CheckExecutionRequest request, CheckResult result) {
        CheckStep configStep = new CheckStep("检查项目名称格式", "检查项目名称是否以#结尾", "CONFIG_CHECK");
        configStep.setStatus("RUNNING");
        result.addCheckStep(configStep);

        DatabaseConfig dbConfig = request.getDatabaseConfig();
        if (dbConfig == null || dbConfig.getProjectName() == null) {
            configStep.setSuccess(false);
            configStep.setErrorMessage("数据库配置或项目名称缺失");
            result.setActualResult("❌ 数据库配置或项目名称缺失");
            return false;
        }

        String projectName = dbConfig.getProjectName();
        boolean formatOk = !projectName.endsWith("#");

        String formatResult;
        if (formatOk) {
            formatResult = "✅ 项目名称格式正常: " + projectName;
        } else {
            formatResult = "❌ 项目名称以#结尾，建议修改: " + projectName;
        }

        configStep.setSuccess(formatOk);
        configStep.setResult(formatResult);
        result.setActualResult(formatResult);

        return formatOk;
    }

    /**
     * 检查pecdeviceextend表重复数据
     */
    private boolean executePecdeviceextendDuplicateCheck(CheckExecutionRequest request, CheckResult result,
                                                        Map<String, Boolean> databaseConnections) {
        // 步骤1：获取数据库配置
        CheckStep configStep = new CheckStep("获取数据库配置", "从请求中获取数据库连接信息", "DB_CONNECT");
        configStep.setStatus("RUNNING");
        result.addCheckStep(configStep);

        DatabaseConfig dbConfig = request.getDatabaseConfig();
        if (dbConfig == null || dbConfig.getProjectName() == null) {
            configStep.setSuccess(false);
            configStep.setErrorMessage("数据库配置或项目名称缺失，无法连接Matterhorn数据库");
            result.setActualResult("❌ 数据库配置或项目名称缺失，无法检查设备数据");
            return false;
        }

        // 创建Matterhorn数据库配置
        DatabaseConfig matterhornConfig = new DatabaseConfig();
        matterhornConfig.setUrl(dbConfig.getUrl());
        matterhornConfig.setPort(dbConfig.getPort());
        matterhornConfig.setUsername(dbConfig.getUsername());
        matterhornConfig.setPassword(dbConfig.getPassword());
        matterhornConfig.setDatabase("Matterhorn_" + dbConfig.getProjectName());

        configStep.setSuccess(true);
        configStep.setResult("连接Matterhorn数据库: " + matterhornConfig.getDatabase());

        // 步骤2：连接Matterhorn数据库
        CheckStep dbConnectStep = new CheckStep("连接Matterhorn数据库", "连接到Matterhorn_" + dbConfig.getProjectName(), "DB_CONNECT");
        dbConnectStep.setStatus("RUNNING");
        result.addCheckStep(dbConnectStep);

        String dbKey = matterhornConfig.getUrl() + ":" + matterhornConfig.getPort() + ":" + matterhornConfig.getDatabase();
        boolean dbConnected;
        if (databaseConnections.containsKey(dbKey)) {
            dbConnected = databaseConnections.get(dbKey);
            dbConnectStep.setResult("使用已有数据库连接");
        } else {
            dbConnected = databaseService.testConnection(matterhornConfig);
            databaseConnections.put(dbKey, dbConnected);
            dbConnectStep.setResult(dbConnected ? "Matterhorn数据库连接成功" : "Matterhorn数据库连接失败: " + matterhornConfig.getErrorMessage());
        }

        dbConnectStep.setSuccess(dbConnected);

        if (!dbConnected) {
            result.setActualResult("❌ 无法连接到Matterhorn数据库，请检查项目名称和数据库配置");
            return false;
        }

        // 步骤3：查询重复数据
        CheckStep queryStep = new CheckStep("查询重复设备数据", "查询pecdeviceextend表重复的deviceid", "DB_QUERY");
        queryStep.setStatus("RUNNING");
        result.addCheckStep(queryStep);

        String sql = "SELECT deviceid, COUNT(*) as count FROM pecdeviceextend GROUP BY deviceid HAVING COUNT(*) > 1";
        queryStep.setCommand(sql);

        String queryResult = databaseService.executeQuery(matterhornConfig, sql);
        queryStep.setSuccess(true);
        queryStep.setResult(queryResult);

        // 分析结果
        boolean noDuplicates = queryResult.contains("(无数据)") || queryResult.contains("共 0 行数据") || queryResult.trim().isEmpty();
        String analysisResult;
        if (noDuplicates) {
            analysisResult = "✅ 设备数据正常，未发现重复的deviceid";
        } else {
            analysisResult = "❌ 发现重复的设备ID，这可能导致数据统计错误，请及时处理:\n" + queryResult;
        }

        result.setActualResult(analysisResult);
        return noDuplicates;
    }

    /**
     * 检查告警点名称下划线
     */
    private boolean executeAlarmPointUnderscoreCheck(CheckExecutionRequest request, CheckResult result,
                                                    Map<String, Boolean> databaseConnections) {
        // 步骤1：获取数据库配置
        CheckStep configStep = new CheckStep("获取数据库配置", "从请求中获取数据库连接信息", "DB_CONNECT");
        configStep.setStatus("RUNNING");
        result.addCheckStep(configStep);

        DatabaseConfig dbConfig = request.getDatabaseConfig();
        if (dbConfig == null || dbConfig.getProjectName() == null) {
            configStep.setSuccess(false);
            configStep.setErrorMessage("数据库配置或项目名称缺失，无法连接Matterhorn数据库");
            result.setActualResult("❌ 数据库配置或项目名称缺失，无法检查告警点配置");
            return false;
        }

        // 创建Matterhorn数据库配置
        DatabaseConfig matterhornConfig = new DatabaseConfig();
        matterhornConfig.setUrl(dbConfig.getUrl());
        matterhornConfig.setPort(dbConfig.getPort());
        matterhornConfig.setUsername(dbConfig.getUsername());
        matterhornConfig.setPassword(dbConfig.getPassword());
        matterhornConfig.setDatabase("Matterhorn_" + dbConfig.getProjectName());

        configStep.setSuccess(true);
        configStep.setResult("连接Matterhorn数据库: " + matterhornConfig.getDatabase());

        // 步骤2：连接Matterhorn数据库
        CheckStep dbConnectStep = new CheckStep("连接Matterhorn数据库", "连接到Matterhorn_" + dbConfig.getProjectName(), "DB_CONNECT");
        dbConnectStep.setStatus("RUNNING");
        result.addCheckStep(dbConnectStep);

        String dbKey = matterhornConfig.getUrl() + ":" + matterhornConfig.getPort() + ":" + matterhornConfig.getDatabase();
        boolean dbConnected;
        if (databaseConnections.containsKey(dbKey)) {
            dbConnected = databaseConnections.get(dbKey);
            dbConnectStep.setResult("使用已有数据库连接");
        } else {
            dbConnected = databaseService.testConnection(matterhornConfig);
            databaseConnections.put(dbKey, dbConnected);
            dbConnectStep.setResult(dbConnected ? "Matterhorn数据库连接成功" : "Matterhorn数据库连接失败: " + matterhornConfig.getErrorMessage());
        }

        dbConnectStep.setSuccess(dbConnected);

        if (!dbConnected) {
            result.setActualResult("❌ 无法连接到Matterhorn数据库，请检查项目名称和数据库配置");
            return false;
        }

        // 步骤3：查询包含下划线的告警点名称
        CheckStep queryStep = new CheckStep("查询告警点名称", "查询thresholdmeasurepoint表包含下划线的dataname", "DB_QUERY");
        queryStep.setStatus("RUNNING");
        result.addCheckStep(queryStep);

        // 修复SQL：使用ESCAPE子句正确匹配下划线
        String sql = "SELECT dataname FROM thresholdmeasurepoint WHERE dataname LIKE '%\\_%' ESCAPE '\\'";
        queryStep.setCommand(sql);

        String queryResult = databaseService.executeQuery(matterhornConfig, sql);
        queryStep.setSuccess(true);
        queryStep.setResult(queryResult);

        // 分析结果
        boolean noUnderscores = queryResult.contains("(无数据)") || queryResult.contains("共 0 行数据") || queryResult.trim().isEmpty();
        String analysisResult;
        if (noUnderscores) {
            analysisResult = "✅ 告警点名称规范，未发现包含下划线的名称";
        } else {
            analysisResult = "❌ 发现不规范的告警点名称（包含下划线），建议修改为更规范的命名:\n" + queryResult;
        }

        result.setActualResult(analysisResult);
        return noUnderscores;
    }

    /**
     * 检查服务日志异常
     */
    private boolean checkServiceLogExceptions(CheckExecutionRequest request, CheckResult result,
                                            Map<String, Boolean> serverConnections) {
        boolean allLogsOk = true;
        StringBuilder resultBuilder = new StringBuilder();

        // 检查VIP服务器
        allLogsOk &= checkSingleServerLogExceptions(request.getVipServer(), "VIP服务器",
                result, serverConnections, resultBuilder);

        result.setActualResult(resultBuilder.toString());
        return allLogsOk;
    }

    /**
     * 检查单台服务器的服务日志异常
     */
    private boolean checkSingleServerLogExceptions(ServerInfo server, String serverName,
                                                  CheckResult result, Map<String, Boolean> serverConnections,
                                                  StringBuilder resultBuilder) {
        if (server == null || server.getHost() == null) {
            resultBuilder.append(serverName).append(": 配置信息缺失\n");
            return false;
        }

        CheckStep logStep = new CheckStep("检查" + serverName + "服务日志", "检查Docker服务日志异常", "SSH_COMMAND");
        logStep.setStatus("RUNNING");
        result.addCheckStep(logStep);

        String serverKey = server.getHost() + ":" + server.getPort();
        if (!serverConnections.getOrDefault(serverKey, false)) {
            boolean connected = sshService.testConnection(server);
            serverConnections.put(serverKey, connected);
            if (!connected) {
                logStep.setSuccess(false);
                logStep.setErrorMessage("服务器连接失败，无法检查日志");
                resultBuilder.append(serverName).append(": ❌ 服务器连接失败，无法检查服务日志\n");
                return false;
            }
        }

        // 获取运行中的容器
        String containers = sshService.executeCommand(server, "docker ps --format '{{.Names}}' | head -10");
        if (containers.trim().isEmpty()) {
            logStep.setSuccess(true);
            logStep.setResult("无运行中的Docker容器");
            resultBuilder.append(serverName).append(": ⚠️ 无运行中的Docker容器\n");
            return true;
        }

        boolean hasExceptions = false;
        StringBuilder exceptions = new StringBuilder();

        // 检查每个容器的日志
        String[] containerList = containers.trim().split("\n");
        for (String containerName : containerList) {
            containerName = containerName.trim();
            if (!containerName.isEmpty()) {
                String logCommand = "docker logs --tail 500 " + containerName + " 2>&1 | grep -i -E '(exception|error|fatal)' | tail -5";
                String logResult = sshService.executeCommand(server, logCommand);

                if (!logResult.trim().isEmpty()) {
                    hasExceptions = true;
                    exceptions.append("\n【").append(containerName).append("】服务异常:\n");
                    exceptions.append(logResult.trim()).append("\n");
                }
            }
        }

        logStep.setSuccess(!hasExceptions);
        logStep.setResult(hasExceptions ? "发现服务异常" : "服务日志正常");

        if (hasExceptions) {
            resultBuilder.append(serverName).append(": ❌ 发现服务异常，请及时处理:").append(exceptions.toString()).append("\n");
        } else {
            resultBuilder.append(serverName).append(": ✅ 所有服务日志正常，未发现异常信息\n");
        }

        return !hasExceptions;
    }

    /**
     * 检查SSH免密登录
     */
    private boolean checkSSHPasswordless(CheckExecutionRequest request, CheckResult result,
                                       Map<String, Boolean> serverConnections) {
        CheckStep checkStep = new CheckStep("检查业务服务器免密登录", "检查业务主备服务器之间免密登录", "SSH_COMMAND");
        checkStep.setStatus("RUNNING");
        result.addCheckStep(checkStep);

        ServerInfo mainServer = request.getMainServer();
        ServerInfo backupServer = request.getBackupServer();

        if (mainServer == null || backupServer == null) {
            checkStep.setSuccess(false);
            checkStep.setErrorMessage("业务服务器配置缺失");
            result.setActualResult("❌ 业务服务器配置缺失，无法检查免密登录");
            return false;
        }

        boolean allOk = true;
        StringBuilder resultBuilder = new StringBuilder();

        // 检查主服务器到备服务器的免密登录
        String mainKey = mainServer.getHost() + ":" + mainServer.getPort();
        if (!serverConnections.getOrDefault(mainKey, false)) {
            boolean connected = sshService.testConnection(mainServer);
            serverConnections.put(mainKey, connected);
            if (!connected) {
                resultBuilder.append("❌ 业务主服务器连接失败，无法检查免密登录\n");
                allOk = false;
            }
        }

        if (allOk) {
            String sshTestCommand = "ssh -o BatchMode=yes -o ConnectTimeout=5 " + backupServer.getHost() + " 'echo success' 2>/dev/null || echo 'failed'";
            String sshResult = sshService.executeCommand(mainServer, sshTestCommand);

            if (sshResult.trim().equals("success")) {
                resultBuilder.append("✅ 业务主服务器到备服务器免密登录正常\n");
            } else {
                resultBuilder.append("❌ 业务主服务器到备服务器免密登录失败，请配置SSH密钥\n");
                allOk = false;
            }
        }

        // 检查备服务器到主服务器的免密登录
        String backupKey = backupServer.getHost() + ":" + backupServer.getPort();
        if (!serverConnections.getOrDefault(backupKey, false)) {
            boolean connected = sshService.testConnection(backupServer);
            serverConnections.put(backupKey, connected);
            if (!connected) {
                resultBuilder.append("❌ 业务备服务器连接失败，无法检查免密登录\n");
                allOk = false;
            }
        }

        if (serverConnections.getOrDefault(backupKey, false)) {
            String sshTestCommand = "ssh -o BatchMode=yes -o ConnectTimeout=5 " + mainServer.getHost() + " 'echo success' 2>/dev/null || echo 'failed'";
            String sshResult = sshService.executeCommand(backupServer, sshTestCommand);

            if (sshResult.trim().equals("success")) {
                resultBuilder.append("✅ 业务备服务器到主服务器免密登录正常\n");
            } else {
                resultBuilder.append("❌ 业务备服务器到主服务器免密登录失败，请配置SSH密钥\n");
                allOk = false;
            }
        }

        checkStep.setSuccess(allOk);
        checkStep.setResult(allOk ? "免密登录检查通过" : "免密登录检查失败");
        result.setActualResult(resultBuilder.toString());

        return allOk;
    }

    /**
     * 检查文件同步
     */
    private boolean checkFileSync(CheckExecutionRequest request, CheckResult result,
                                Map<String, Boolean> serverConnections) {
        CheckStep checkStep = new CheckStep("检查业务服务器文件同步", "检查文件管理器目录同步", "SSH_COMMAND");
        checkStep.setStatus("RUNNING");
        result.addCheckStep(checkStep);

        ServerInfo mainServer = request.getMainServer();
        ServerInfo backupServer = request.getBackupServer();

        if (mainServer == null || backupServer == null) {
            checkStep.setSuccess(false);
            checkStep.setErrorMessage("业务服务器配置缺失");
            result.setActualResult("❌ 业务服务器配置缺失，无法检查文件同步");
            return false;
        }

        // 获取主服务器文件列表
        String mainKey = mainServer.getHost() + ":" + mainServer.getPort();
        if (!serverConnections.getOrDefault(mainKey, false)) {
            boolean connected = sshService.testConnection(mainServer);
            serverConnections.put(mainKey, connected);
            if (!connected) {
                result.setActualResult("❌ 业务主服务器连接失败，无法检查文件同步");
                return false;
            }
        }

        String mainFiles = sshService.executeCommand(mainServer, "ls -1 /var/cache/CET/filemanager/ 2>/dev/null | sort");

        // 获取备服务器文件列表
        String backupKey = backupServer.getHost() + ":" + backupServer.getPort();
        if (!serverConnections.getOrDefault(backupKey, false)) {
            boolean connected = sshService.testConnection(backupServer);
            serverConnections.put(backupKey, connected);
            if (!connected) {
                result.setActualResult("❌ 业务备服务器连接失败，无法检查文件同步");
                return false;
            }
        }

        String backupFiles = sshService.executeCommand(backupServer, "ls -1 /var/cache/CET/filemanager/ 2>/dev/null | sort");

        // 比较文件列表
        Set<String> mainFileSet = new HashSet<>();
        Set<String> backupFileSet = new HashSet<>();

        if (!mainFiles.trim().isEmpty()) {
            for (String file : mainFiles.trim().split("\n")) {
                if (!file.trim().isEmpty()) {
                    mainFileSet.add(file.trim());
                }
            }
        }

        if (!backupFiles.trim().isEmpty()) {
            for (String file : backupFiles.trim().split("\n")) {
                if (!file.trim().isEmpty()) {
                    backupFileSet.add(file.trim());
                }
            }
        }

        Set<String> onlyInMain = new HashSet<>(mainFileSet);
        onlyInMain.removeAll(backupFileSet);

        Set<String> onlyInBackup = new HashSet<>(backupFileSet);
        onlyInBackup.removeAll(mainFileSet);

        boolean syncOk = onlyInMain.isEmpty() && onlyInBackup.isEmpty();
        StringBuilder resultBuilder = new StringBuilder();

        if (syncOk) {
            resultBuilder.append("✅ 业务主备服务器文件管理器目录同步正常，共 ").append(mainFileSet.size()).append(" 个文件");
        } else {
            resultBuilder.append("❌ 业务主备服务器文件管理器目录不同步:\n");

            if (!onlyInMain.isEmpty()) {
                resultBuilder.append("\n仅在主服务器存在的文件:\n");
                for (String file : onlyInMain) {
                    resultBuilder.append("  - ").append(file).append("\n");
                }
            }

            if (!onlyInBackup.isEmpty()) {
                resultBuilder.append("\n仅在备服务器存在的文件:\n");
                for (String file : onlyInBackup) {
                    resultBuilder.append("  - ").append(file).append("\n");
                }
            }
        }

        checkStep.setSuccess(syncOk);
        checkStep.setResult(syncOk ? "文件同步正常" : "文件同步异常");
        result.setActualResult(resultBuilder.toString());

        return syncOk;
    }

    /**
     * 检查netclient版本
     */
    private boolean checkNetclientVersion(CheckExecutionRequest request, CheckResult result,
                                        Map<String, Boolean> serverConnections) {
        String targetVersion = "v1.13.18";
        boolean allVersionsOk = true;
        StringBuilder resultBuilder = new StringBuilder();

        // 检查采集主服务器
        allVersionsOk &= checkSingleServerNetclientVersion(request.getCollectMainServer(), "采集主服务器",
                targetVersion, result, serverConnections, resultBuilder);

        // 检查采集备服务器
        allVersionsOk &= checkSingleServerNetclientVersion(request.getCollectBackupServer(), "采集备服务器",
                targetVersion, result, serverConnections, resultBuilder);

        result.setActualResult(resultBuilder.toString());
        return allVersionsOk;
    }

    /**
     * 检查单台服务器的netclient版本
     */
    private boolean checkSingleServerNetclientVersion(ServerInfo server, String serverName, String targetVersion,
                                                     CheckResult result, Map<String, Boolean> serverConnections,
                                                     StringBuilder resultBuilder) {
        if (server == null || server.getHost() == null) {
            resultBuilder.append(serverName).append(": 配置信息缺失\n");
            return false;
        }

        CheckStep versionStep = new CheckStep("检查" + serverName + "netclient版本", "获取运行中的netclient版本", "SSH_COMMAND");
        versionStep.setCommand("docker ps --format 'table {{.Image}}' | grep netclient");
        versionStep.setStatus("RUNNING");
        result.addCheckStep(versionStep);

        String serverKey = server.getHost() + ":" + server.getPort();
        if (!serverConnections.getOrDefault(serverKey, false)) {
            boolean connected = sshService.testConnection(server);
            serverConnections.put(serverKey, connected);
            if (!connected) {
                versionStep.setSuccess(false);
                versionStep.setErrorMessage("服务器连接失败");
                resultBuilder.append(serverName).append(": ❌ 服务器连接失败，无法检查netclient版本\n");
                return false;
            }
        }

        String runningContainer = sshService.executeCommand(server, "docker ps --format 'table {{.Image}}' | grep netclient");
        runningContainer = runningContainer.trim();

        String currentVersion = "";
        if (!runningContainer.isEmpty()) {
            // 从容器镜像名中提取版本号
            String[] parts = runningContainer.split(":");
            if (parts.length > 1) {
                currentVersion = parts[1].trim();
            }
        }

        versionStep.setSuccess(true);
        versionStep.setResult(runningContainer.isEmpty() ? "netclient服务未运行" : "运行版本: " + currentVersion);

        boolean versionOk = true;
        if (runningContainer.isEmpty()) {
            resultBuilder.append(serverName).append(": ❌ netclient服务未运行，请检查服务状态\n");
            versionOk = false;
        } else if (currentVersion.isEmpty()) {
            resultBuilder.append(serverName).append(": ❌ 无法获取netclient版本信息\n");
            versionOk = false;
        } else {
            // 版本比较
            if (compareVersions(currentVersion, targetVersion) < 0) {
                resultBuilder.append(serverName).append(": ❌ netclient版本过低 ").append(currentVersion)
                           .append("，需要更新到 ").append(targetVersion).append(" 或更高版本\n");
                versionOk = false;
            } else {
                resultBuilder.append(serverName).append(": ✅ netclient版本正常 ").append(currentVersion).append("\n");
            }
        }

        return versionOk;
    }

    /**
     * 检查告警权限推送配置
     */
    private boolean checkAlarmAuthPush(CheckExecutionRequest request, CheckResult result) {
        return checkVipServerConfig(request, result, "告警权限推送配置",
                "grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'cet_auth-push'",
                "cet_auth-push", "告警权限推送功能");
    }

    /**
     * 检查告警订阅配置
     */
    private boolean checkAlarmSubscription(CheckExecutionRequest request, CheckResult result) {
        return checkVipServerConfig(request, result, "告警订阅配置",
                "grep -A 200 'datacenter-common-alarmer:' /etc/CET/docker/docker-compose.yml | grep 'sms_swtich'",
                "sms_swtich", "告警订阅功能");
    }

    /**
     * 检查VIP服务器配置的通用方法
     */
    private boolean checkVipServerConfig(CheckExecutionRequest request, CheckResult result,
                                       String stepName, String command, String configKey, String featureName) {
        CheckStep configStep = new CheckStep(stepName, "检查VIP服务器配置", "SSH_COMMAND");
        configStep.setCommand(command);
        configStep.setStatus("RUNNING");
        result.addCheckStep(configStep);

        ServerInfo vipServer = request.getVipServer();
        if (vipServer == null) {
            configStep.setSuccess(false);
            configStep.setErrorMessage("VIP服务器配置缺失");
            result.setActualResult("❌ VIP服务器配置缺失，无法检查" + featureName);
            return false;
        }

        try {
            boolean connected = sshService.testConnection(vipServer);
            if (!connected) {
                configStep.setSuccess(false);
                configStep.setErrorMessage("VIP服务器连接失败");
                result.setActualResult("❌ VIP服务器连接失败，无法检查" + featureName);
                return false;
            }

            String configResult = sshService.executeCommand(vipServer, command);
            configStep.setSuccess(true);
            configStep.setResult(configResult.trim().isEmpty() ? "未找到配置项" : configResult.trim());

            boolean isEnabled = false;
            String analysisResult;

            if (configResult.trim().isEmpty()) {
                analysisResult = "❌ " + featureName + "未配置，建议添加配置项启用该功能";
            } else if (configResult.contains("true")) {
                isEnabled = true;
                analysisResult = "✅ " + featureName + "已开启";
            } else {
                analysisResult = "❌ " + featureName + "未开启，当前配置: " + configResult.trim();
            }

            result.setActualResult(analysisResult);
            return isEnabled;

        } catch (Exception e) {
            configStep.setSuccess(false);
            configStep.setErrorMessage(e.getMessage());
            result.setActualResult("❌ 检查" + featureName + "时发生错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查onlyreport配置文件
     */
    private boolean checkOnlyReportConfig(CheckExecutionRequest request, CheckResult result) {
        CheckStep configStep = new CheckStep("检查报表配置文件", "检查only-report配置文件", "SSH_COMMAND");
        configStep.setCommand("ls /etc/CET/only-report/config/");
        configStep.setStatus("RUNNING");
        result.addCheckStep(configStep);

        ServerInfo vipServer = request.getVipServer();
        if (vipServer == null) {
            configStep.setSuccess(false);
            configStep.setErrorMessage("VIP服务器配置缺失");
            result.setActualResult("❌ VIP服务器配置缺失，无法检查报表配置");
            return false;
        }

        try {
            boolean connected = sshService.testConnection(vipServer);
            if (!connected) {
                configStep.setSuccess(false);
                configStep.setErrorMessage("VIP服务器连接失败");
                result.setActualResult("❌ VIP服务器连接失败，无法检查报表配置");
                return false;
            }

            String fileList = sshService.executeCommand(vipServer, "ls /etc/CET/only-report/config/ 2>/dev/null || echo 'directory not found'");
            configStep.setSuccess(true);
            configStep.setResult(fileList.trim());

            boolean hasMthmeta = fileList.contains("mthmeta.json");
            boolean hasReportType = fileList.contains("reportType.json");

            String analysisResult;
            if (hasMthmeta && hasReportType) {
                analysisResult = "✅ 报表配置文件完整，包含mthmeta.json和reportType.json";
            } else {
                StringBuilder missing = new StringBuilder();
                missing.append("❌ 报表配置文件不完整，缺少:");
                if (!hasMthmeta) {
                    missing.append(" mthmeta.json");
                }
                if (!hasReportType) {
                    missing.append(" reportType.json");
                }
                missing.append("，请补充相关配置文件");
                analysisResult = missing.toString();
            }

            result.setActualResult(analysisResult);
            return hasMthmeta && hasReportType;

        } catch (Exception e) {
            configStep.setSuccess(false);
            configStep.setErrorMessage(e.getMessage());
            result.setActualResult("❌ 检查报表配置文件时发生错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 检查docker-compose.yml文件中的tab字符
     */
    private boolean checkDockerComposeTabCharacters(CheckExecutionRequest request, CheckResult result) {
        CheckStep configStep = new CheckStep("检查Docker配置文件格式", "检查docker-compose.yml文件tab字符", "SSH_COMMAND");
        configStep.setCommand("grep -n $'\\t' /etc/CET/docker/docker-compose.yml");
        configStep.setStatus("RUNNING");
        result.addCheckStep(configStep);

        ServerInfo vipServer = request.getVipServer();
        if (vipServer == null) {
            configStep.setSuccess(false);
            configStep.setErrorMessage("VIP服务器配置缺失");
            result.setActualResult("❌ VIP服务器配置缺失，无法检查Docker配置文件");
            return false;
        }

        try {
            boolean connected = sshService.testConnection(vipServer);
            if (!connected) {
                configStep.setSuccess(false);
                configStep.setErrorMessage("VIP服务器连接失败");
                result.setActualResult("❌ VIP服务器连接失败，无法检查Docker配置文件");
                return false;
            }

            String tabResult = sshService.executeCommand(vipServer, "grep -n $'\\t' /etc/CET/docker/docker-compose.yml 2>/dev/null || echo 'no tabs found'");
            configStep.setSuccess(true);
            configStep.setResult(tabResult.trim());

            boolean hasTabCharacters = !tabResult.contains("no tabs found") && !tabResult.trim().isEmpty();

            String analysisResult;
            if (hasTabCharacters) {
                analysisResult = "❌ Docker配置文件包含tab字符，这可能导致配置解析错误，建议替换为空格:\n" + tabResult.trim();
            } else {
                analysisResult = "✅ Docker配置文件格式正确，未发现tab字符";
            }

            result.setActualResult(analysisResult);
            return !hasTabCharacters;

        } catch (Exception e) {
            configStep.setSuccess(false);
            configStep.setErrorMessage(e.getMessage());
            result.setActualResult("❌ 检查Docker配置文件时发生错误: " + e.getMessage());
            return false;
        }
    }
}
