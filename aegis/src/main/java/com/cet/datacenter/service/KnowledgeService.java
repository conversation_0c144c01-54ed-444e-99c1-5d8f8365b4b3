package com.cet.datacenter.service;

import com.cet.datacenter.entity.KnowledgeItem;
import com.cet.datacenter.util.MarkdownUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 知识库服务类
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@Service
public class KnowledgeService {
    
    private static final Logger logger = LoggerFactory.getLogger(KnowledgeService.class);
    
    @Autowired
    private ResourceLoader resourceLoader;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Autowired
    private MarkdownUtil markdownUtil;
    
    @Value("${aegis.knowledge.data-path}")
    private String knowledgeDataPath;
    
    @Value("${aegis.knowledge.solutions-path}")
    private String solutionsPath;
    
    private List<KnowledgeItem> knowledgeItems = new ArrayList<>();
    
    @PostConstruct
    public void init() {
        loadKnowledgeItems();
    }
    
    /**
     * 加载知识库数据
     */
    private void loadKnowledgeItems() {
        try {
            Resource resource = resourceLoader.getResource(knowledgeDataPath);
            if (resource.exists()) {
                knowledgeItems = objectMapper.readValue(resource.getInputStream(), 
                        new TypeReference<List<KnowledgeItem>>() {});
                logger.info("加载知识库数据成功，共{}个知识条目", knowledgeItems.size());
            } else {
                logger.warn("知识库数据文件不存在: {}", knowledgeDataPath);
                createDefaultKnowledgeItems();
            }
        } catch (IOException e) {
            logger.error("加载知识库数据失败: {}", e.getMessage());
            createDefaultKnowledgeItems();
        }
    }
    
    /**
     * 创建默认知识库数据
     */
    private void createDefaultKnowledgeItems() {
        knowledgeItems = new ArrayList<>();
        
        // 系统问题
        KnowledgeItem systemLoad = new KnowledgeItem("sys_high_load", "系统负载过高", "系统负载过高的排查和解决方案", "系统");
        systemLoad.setKeywords(Arrays.asList("负载", "load", "cpu", "性能"));
        systemLoad.setSolutionFile("system-high-load.md");
        systemLoad.setColor("#ff6b6b");
        systemLoad.setPriority(10);
        knowledgeItems.add(systemLoad);

        KnowledgeItem memoryIssue = new KnowledgeItem("sys_memory_full", "内存不足", "内存不足的排查和解决方案", "系统");
        memoryIssue.setKeywords(Arrays.asList("内存", "memory", "oom", "swap"));
        memoryIssue.setSolutionFile("system-memory-full.md");
        memoryIssue.setColor("#4ecdc4");
        memoryIssue.setPriority(9);
        knowledgeItems.add(memoryIssue);

        KnowledgeItem diskFull = new KnowledgeItem("sys_disk_full", "磁盘空间不足", "磁盘空间不足的排查和解决方案", "系统");
        diskFull.setKeywords(Arrays.asList("磁盘", "disk", "空间", "满"));
        diskFull.setSolutionFile("system-disk-full.md");
        diskFull.setColor("#45b7d1");
        diskFull.setPriority(8);
        knowledgeItems.add(diskFull);

        // 网络问题
        KnowledgeItem networkIssue = new KnowledgeItem("net_connection_fail", "网络连接失败", "网络连接问题的排查和解决方案", "网络");
        networkIssue.setKeywords(Arrays.asList("网络", "连接", "ping", "dns"));
        networkIssue.setSolutionFile("network-connection-fail.md");
        networkIssue.setColor("#96ceb4");
        networkIssue.setPriority(7);
        knowledgeItems.add(networkIssue);

        // 服务问题
        KnowledgeItem serviceDown = new KnowledgeItem("service_down", "服务停止", "服务停止的排查和解决方案", "服务");
        serviceDown.setKeywords(Arrays.asList("服务", "service", "停止", "启动"));
        serviceDown.setSolutionFile("service-down.md");
        serviceDown.setColor("#feca57");
        serviceDown.setPriority(6);
        knowledgeItems.add(serviceDown);
        
        logger.info("创建默认知识库数据成功，共{}个知识条目", knowledgeItems.size());
    }
    
    /**
     * 获取所有知识条目
     */
    public List<KnowledgeItem> getAllKnowledgeItems() {
        return knowledgeItems.stream()
                .filter(KnowledgeItem::isEnabled)
                .sorted((a, b) -> Integer.compare(b.getPriority(), a.getPriority()))
                .collect(Collectors.toList());
    }
    
    /**
     * 根据分类获取知识条目
     */
    public List<KnowledgeItem> getKnowledgeItemsByCategory(String category) {
        return knowledgeItems.stream()
                .filter(item -> item.isEnabled() && category.equals(item.getCategory()))
                .sorted((a, b) -> Integer.compare(b.getPriority(), a.getPriority()))
                .collect(Collectors.toList());
    }
    
    /**
     * 根据关键词搜索知识条目
     */
    public List<KnowledgeItem> searchKnowledgeItems(String keyword) {
        String lowerKeyword = keyword.toLowerCase();
        return knowledgeItems.stream()
                .filter(item -> item.isEnabled())
                .filter(item -> 
                    item.getTitle().toLowerCase().contains(lowerKeyword) ||
                    item.getDescription().toLowerCase().contains(lowerKeyword) ||
                    (item.getKeywords() != null && item.getKeywords().stream()
                            .anyMatch(k -> k.toLowerCase().contains(lowerKeyword)))
                )
                .sorted((a, b) -> Integer.compare(b.getPriority(), a.getPriority()))
                .collect(Collectors.toList());
    }
    
    /**
     * 根据ID获取知识条目
     */
    public KnowledgeItem getKnowledgeItemById(String id) {
        return knowledgeItems.stream()
                .filter(item -> id.equals(item.getId()))
                .findFirst()
                .orElse(null);
    }
    
    /**
     * 获取解决方案内容
     */
    public String getSolutionContent(String solutionFile) {
        try {
            String resourcePath = solutionsPath + solutionFile;
            Resource resource = resourceLoader.getResource(resourcePath);
            
            if (resource.exists()) {
                String markdown = IOUtils.toString(resource.getInputStream(), StandardCharsets.UTF_8);
                return markdownUtil.markdownToHtml(markdown);
            } else {
                logger.warn("解决方案文件不存在: {}", resourcePath);
                return createDefaultSolution(solutionFile);
            }
            
        } catch (IOException e) {
            logger.error("读取解决方案文件失败: {} - {}", solutionFile, e.getMessage());
            return "<p class=\"error\">解决方案加载失败: " + e.getMessage() + "</p>";
        }
    }
    
    /**
     * 创建默认解决方案内容
     */
    private String createDefaultSolution(String solutionFile) {
        String defaultMarkdown = "# 解决方案\n\n" +
                "抱歉，该解决方案文档尚未创建。\n\n" +
                "## 建议步骤\n\n" +
                "1. 检查系统日志\n" +
                "2. 分析错误信息\n" +
                "3. 查找相关文档\n" +
                "4. 联系技术支持\n\n" +
                "**文件名**: " + solutionFile;
        
        return markdownUtil.markdownToHtml(defaultMarkdown);
    }
}
