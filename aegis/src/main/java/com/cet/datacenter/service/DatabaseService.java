package com.cet.datacenter.service;

import com.cet.datacenter.entity.DatabaseConfig;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据库服务类
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
public class DatabaseService {
    
    private static final Logger logger = LoggerFactory.getLogger(DatabaseService.class);
    
    // 数据库连接池缓存
    private final Map<String, DataSource> dataSourceCache = new ConcurrentHashMap<>();
    
    /**
     * 测试数据库连接
     */
    public boolean testConnection(DatabaseConfig config) {
        try (Connection conn = getConnection(config)) {
            return conn != null && !conn.isClosed();
        } catch (Exception e) {
            logger.error("测试数据库连接失败: {}", e.getMessage());
            config.setErrorMessage(e.getMessage());
            return false;
        }
    }
    
    /**
     * 执行SQL查询
     */
    public String executeQuery(DatabaseConfig config, String sql) {
        StringBuilder result = new StringBuilder();
        
        try (Connection conn = getConnection(config);
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            // 获取列信息
            int columnCount = rs.getMetaData().getColumnCount();
            
            // 添加列标题
            for (int i = 1; i <= columnCount; i++) {
                if (i > 1) result.append("\t");
                result.append(rs.getMetaData().getColumnName(i));
            }
            result.append("\n");
            
            // 添加分隔线
            for (int i = 1; i <= columnCount; i++) {
                if (i > 1) result.append("\t");
                result.append("--------");
            }
            result.append("\n");
            
            // 添加数据行
            int rowCount = 0;
            while (rs.next() && rowCount < 100) { // 限制最多100行
                for (int i = 1; i <= columnCount; i++) {
                    if (i > 1) result.append("\t");
                    Object value = rs.getObject(i);
                    result.append(value != null ? value.toString() : "NULL");
                }
                result.append("\n");
                rowCount++;
            }
            
            if (rowCount == 0) {
                result.append("(无数据)\n");
            } else if (rowCount == 100) {
                result.append("... (结果已截断，仅显示前100行)\n");
            }
            
            result.append("\n查询完成，共 ").append(rowCount).append(" 行数据");
            
        } catch (SQLException e) {
            logger.error("执行SQL查询失败: {}", e.getMessage());
            result.append("SQL执行失败: ").append(e.getMessage());
        }
        
        return result.toString();
    }
    
    /**
     * 执行SQL更新操作
     */
    public String executeUpdate(DatabaseConfig config, String sql) {
        try (Connection conn = getConnection(config);
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            int affectedRows = stmt.executeUpdate();
            return "SQL执行成功，影响行数: " + affectedRows;
            
        } catch (SQLException e) {
            logger.error("执行SQL更新失败: {}", e.getMessage());
            return "SQL执行失败: " + e.getMessage();
        }
    }
    
    /**
     * 获取数据库连接
     */
    private Connection getConnection(DatabaseConfig config) throws SQLException {
        String cacheKey = generateCacheKey(config);
        DataSource dataSource = dataSourceCache.get(cacheKey);
        
        if (dataSource == null) {
            dataSource = createDataSource(config);
            dataSourceCache.put(cacheKey, dataSource);
        }
        
        return dataSource.getConnection();
    }
    
    /**
     * 创建数据源
     */
    private DataSource createDataSource(DatabaseConfig config) {
        HikariConfig hikariConfig = new HikariConfig();
        
        hikariConfig.setJdbcUrl(config.getJdbcUrl());
        hikariConfig.setUsername(config.getUsername());
        hikariConfig.setPassword(config.getPassword());
        
        // 连接池配置
        hikariConfig.setMaximumPoolSize(5);
        hikariConfig.setMinimumIdle(1);
        hikariConfig.setConnectionTimeout(10000); // 10秒
        hikariConfig.setIdleTimeout(300000); // 5分钟
        hikariConfig.setMaxLifetime(600000); // 10分钟
        
        // PostgreSQL特定配置
        hikariConfig.setDriverClassName("org.postgresql.Driver");
        hikariConfig.addDataSourceProperty("cachePrepStmts", "true");
        hikariConfig.addDataSourceProperty("prepStmtCacheSize", "250");
        hikariConfig.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        
        return new HikariDataSource(hikariConfig);
    }
    
    /**
     * 生成缓存键
     */
    private String generateCacheKey(DatabaseConfig config) {
        return config.getJdbcUrl() + ":" + config.getUsername();
    }
    
    /**
     * 关闭数据源
     */
    public void closeDataSource(DatabaseConfig config) {
        String cacheKey = generateCacheKey(config);
        DataSource dataSource = dataSourceCache.remove(cacheKey);
        
        if (dataSource instanceof HikariDataSource) {
            ((HikariDataSource) dataSource).close();
            logger.info("关闭数据源: {}", cacheKey);
        }
    }
    
    /**
     * 关闭所有数据源
     */
    public void closeAllDataSources() {
        for (Map.Entry<String, DataSource> entry : dataSourceCache.entrySet()) {
            if (entry.getValue() instanceof HikariDataSource) {
                ((HikariDataSource) entry.getValue()).close();
                logger.info("关闭数据源: {}", entry.getKey());
            }
        }
        dataSourceCache.clear();
    }
    
    /**
     * 获取数据库基本信息
     */
    public Map<String, String> getDatabaseInfo(DatabaseConfig config) {
        Map<String, String> info = new HashMap<>();
        
        try (Connection conn = getConnection(config)) {
            info.put("数据库产品", conn.getMetaData().getDatabaseProductName());
            info.put("数据库版本", conn.getMetaData().getDatabaseProductVersion());
            info.put("驱动名称", conn.getMetaData().getDriverName());
            info.put("驱动版本", conn.getMetaData().getDriverVersion());
            info.put("连接URL", conn.getMetaData().getURL());
            info.put("用户名", conn.getMetaData().getUserName());
            
        } catch (SQLException e) {
            logger.error("获取数据库信息失败: {}", e.getMessage());
            info.put("错误", e.getMessage());
        }
        
        return info;
    }
}
