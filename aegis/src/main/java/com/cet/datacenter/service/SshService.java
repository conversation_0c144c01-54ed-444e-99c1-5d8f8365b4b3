package com.cet.datacenter.service;

import com.cet.datacenter.entity.ServerInfo;
import com.cet.datacenter.util.SshUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * SSH服务类
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@Service
public class SshService {
    
    private static final Logger logger = LoggerFactory.getLogger(SshService.class);
    
    @Autowired
    private SshUtil sshUtil;
    
    /**
     * 测试SSH连接
     */
    public boolean testConnection(ServerInfo serverInfo) {
        try {
            logger.info("开始测试SSH连接: {}", serverInfo.getHost());
            boolean result = sshUtil.testConnection(serverInfo);
            logger.info("SSH连接测试结果: {} - {}", serverInfo.getHost(), result ? "成功" : "失败");
            return result;
        } catch (Exception e) {
            logger.error("SSH连接测试异常: {} - {}", serverInfo.getHost(), e.getMessage());
            return false;
        }
    }
    
    /**
     * 执行SSH命令
     */
    public String executeCommand(ServerInfo serverInfo, String command) {
        try {
            logger.info("执行SSH命令: {} - {}", serverInfo.getHost(), command);
            String result = sshUtil.executeCommand(serverInfo, command);
            logger.info("SSH命令执行成功: {} - 输出长度: {}", serverInfo.getHost(), result.length());
            return result;
        } catch (Exception e) {
            logger.error("SSH命令执行失败: {} - {} - {}", serverInfo.getHost(), command, e.getMessage());
            throw new RuntimeException("SSH命令执行失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 检查文件是否存在
     */
    public boolean checkFileExists(ServerInfo serverInfo, String filePath) {
        try {
            return sshUtil.fileExists(serverInfo, filePath);
        } catch (Exception e) {
            logger.error("文件存在性检查失败: {} - {} - {}", serverInfo.getHost(), filePath, e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查进程是否运行
     */
    public boolean checkProcessRunning(ServerInfo serverInfo, String processName) {
        try {
            return sshUtil.processRunning(serverInfo, processName);
        } catch (Exception e) {
            logger.error("进程运行检查失败: {} - {} - {}", serverInfo.getHost(), processName, e.getMessage());
            return false;
        }
    }
    
    /**
     * 检查端口是否监听
     */
    public boolean checkPortListening(ServerInfo serverInfo, int port) {
        try {
            return sshUtil.portListening(serverInfo, port);
        } catch (Exception e) {
            logger.error("端口监听检查失败: {} - {} - {}", serverInfo.getHost(), port, e.getMessage());
            return false;
        }
    }
}
