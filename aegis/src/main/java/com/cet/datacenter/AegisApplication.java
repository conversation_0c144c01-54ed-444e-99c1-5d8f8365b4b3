package com.cet.datacenter;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import org.springframework.boot.context.properties.EnableConfigurationProperties;

/**
 * Aegis服务器管理系统主启动类
 * 
 * <AUTHOR>
 * @date 2025-08-15 11:30
 */
@SpringBootApplication
@EnableConfigurationProperties
public class AegisApplication {

    private static final String VERSION = "250821"; // 版本号：年月日格式
    private static final int MAX_DAYS_ALLOWED = 30; // 最大允许使用天数

    public static void main(String[] args) {
        // 检查版本是否过期
        if (!checkVersionValidity()) {
            System.err.println("=================================");
            System.err.println("❌ 版本已过期！");
            System.err.println("当前版本: " + VERSION);
            System.err.println("该版本已超过30天使用期限");
            System.err.println("请获取新版本使用");
            System.err.println("=================================");
            System.exit(1);
            return;
        }

        SpringApplication.run(AegisApplication.class, args);
        System.out.println("=================================");
        System.out.println("Aegis服务器管理系统启动成功！");
        System.out.println("版本: " + VERSION);
        System.out.println("访问地址: http://localhost:8080");
        System.out.println("=================================");
    }

    /**
     * 检查版本有效性
     */
    private static boolean checkVersionValidity() {
        try {
            // 解析版本日期 (格式: YYMMDD)
            String year = "20" + VERSION.substring(0, 2);
            String month = VERSION.substring(2, 4);
            String day = VERSION.substring(4, 6);

            LocalDate versionDate = LocalDate.parse(year + "-" + month + "-" + day);
            LocalDate currentDate = LocalDate.now();

            // 计算天数差
            long daysDiff = java.time.temporal.ChronoUnit.DAYS.between(versionDate, currentDate);

            System.out.println("版本日期: " + versionDate);
            System.out.println("当前日期: " + currentDate);
            System.out.println("已使用天数: " + daysDiff);

            return daysDiff <= MAX_DAYS_ALLOWED;

        } catch (Exception e) {
            System.err.println("版本日期解析失败: " + e.getMessage());
            return false;
        }
    }
}
