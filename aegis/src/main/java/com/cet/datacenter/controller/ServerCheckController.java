package com.cet.datacenter.controller;

import com.cet.datacenter.entity.CheckItem;
import com.cet.datacenter.entity.CheckExecutionRequest;
import com.cet.datacenter.entity.CheckResult;
import com.cet.datacenter.entity.DatabaseConfig;
import com.cet.datacenter.entity.ServerInfo;
import com.cet.datacenter.service.DatabaseService;
import com.cet.datacenter.service.ServerCheckService;
import com.cet.datacenter.service.SshService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务器检查控制器
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@Controller
@RequestMapping("/server-check")
public class ServerCheckController {
    
    private static final Logger logger = LoggerFactory.getLogger(ServerCheckController.class);
    
    @Autowired
    private ServerCheckService serverCheckService;

    @Autowired
    private SshService sshService;

    @Autowired
    private DatabaseService databaseService;
    
    @GetMapping
    public String serverCheckPage(Model model) {
        List<CheckItem> checkItems = serverCheckService.getAllCheckItems();
        
        // 按分类分组
        Map<String, List<CheckItem>> itemsByCategory = new HashMap<>();
        for (CheckItem item : checkItems) {
            itemsByCategory.computeIfAbsent(item.getCategory(), k -> new java.util.ArrayList<>()).add(item);
        }
        
        model.addAttribute("title", "服务器检查");
        model.addAttribute("itemsByCategory", itemsByCategory);
        model.addAttribute("serverInfo", new ServerInfo());
        
        return "server-check";
    }
    
    @PostMapping("/test-connection")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> testConnection(@Valid @RequestBody ServerInfo serverInfo) {
        Map<String, Object> response = new HashMap<>();

        try {
            logger.info("测试SSH连接: {}", serverInfo.getHost());
            boolean success = sshService.testConnection(serverInfo);

            response.put("success", success);
            response.put("message", success ? "连接成功" : "连接失败");

            // 如果SSH连接成功，尝试读取配置信息
            if (success) {
                try {
                    // 读取数据库配置
                    DatabaseConfig dbConfig = readDatabaseConfig(serverInfo);
                    if (dbConfig != null) {
                        Map<String, Object> dbInfo = new HashMap<>();
                        dbInfo.put("url", dbConfig.getUrl());
                        dbInfo.put("port", dbConfig.getPort());
                        dbInfo.put("username", dbConfig.getUsername());
                        dbInfo.put("password", dbConfig.getPassword());
                        dbInfo.put("projectName", dbConfig.getProjectName());

                        // 测试数据库连接
                        boolean dbConnected = databaseService.testConnection(dbConfig);
                        dbInfo.put("connected", dbConnected);
                        dbInfo.put("errorMessage", dbConfig.getErrorMessage());

                        response.put("databaseConfig", dbInfo);
                    }

                    // 读取服务器配置
                    Map<String, String> serverConfig = readServerConfig(serverInfo);
                    if (serverConfig != null) {
                        response.put("serverConfig", serverConfig);
                    }

                    if (dbConfig != null && dbConfig.isConnected()) {
                        response.put("message", "服务器连接成功，数据库连接成功");
                    } else if (dbConfig != null) {
                        response.put("message", "服务器连接成功，数据库连接失败: " + dbConfig.getErrorMessage());
                    } else {
                        response.put("message", "服务器连接成功，但无法读取配置信息");
                    }
                } catch (Exception configE) {
                    logger.warn("读取配置信息失败: {}", configE.getMessage());
                    response.put("message", "服务器连接成功，但读取配置信息失败: " + configE.getMessage());
                }
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("测试连接异常: {}", e.getMessage());
            response.put("success", false);
            response.put("message", "连接异常: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 从服务器读取数据库配置
     */
    private DatabaseConfig readDatabaseConfig(ServerInfo serverInfo) {
        try {
            String envContent = sshService.executeCommand(serverInfo, "cat /etc/CET/docker/.env");

            DatabaseConfig config = new DatabaseConfig();
            String[] lines = envContent.split("\n");

            for (String line : lines) {
                line = line.trim();
                if (line.startsWith("DataBaseUrl=")) {
                    config.setUrl(line.substring("DataBaseUrl=".length()));
                } else if (line.startsWith("DataBasePort=")) {
                    config.setPort(line.substring("DataBasePort=".length()));
                } else if (line.startsWith("DataBaseUser=")) {
                    config.setUsername(line.substring("DataBaseUser=".length()));
                } else if (line.startsWith("DataBasePwd=")) {
                    config.setPassword(line.substring("DataBasePwd=".length()));
                } else if (line.startsWith("DBProjectName=")) {
                    config.setProjectName(line.substring("DBProjectName=".length()));
                }
            }

            // 设置默认数据库名
            config.setDatabase("postgres");

            return config;

        } catch (Exception e) {
            logger.error("读取数据库配置失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 从服务器读取服务器配置
     */
    private Map<String, String> readServerConfig(ServerInfo serverInfo) {
        try {
            String envContent = sshService.executeCommand(serverInfo, "cat /etc/CET/docker/.env");

            Map<String, String> config = new HashMap<>();
            String[] lines = envContent.split("\n");

            for (String line : lines) {
                line = line.trim();
                if (line.startsWith("LocalIP=")) {
                    config.put("localIP", line.substring("LocalIP=".length()));
                } else if (line.startsWith("RemoteIP=")) {
                    config.put("remoteIP", line.substring("RemoteIP=".length()));
                } else if (line.startsWith("MasterFrontSCIP=")) {
                    config.put("masterFrontSCIP", line.substring("MasterFrontSCIP=".length()));
                } else if (line.startsWith("SlaveFrontSCIP=")) {
                    config.put("slaveFrontSCIP", line.substring("SlaveFrontSCIP=".length()));
                }
            }

            return config;

        } catch (Exception e) {
            logger.error("读取服务器配置失败: {}", e.getMessage());
            return null;
        }
    }

    @PostMapping("/execute-checks")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> executeChecks(@Valid @RequestBody CheckExecutionRequest request) {
        Map<String, Object> response = new HashMap<>();

        try {
            logger.info("执行检查请求: {}", request);

            if (request.getCheckItems() == null || request.getCheckItems().isEmpty()) {
                response.put("success", false);
                response.put("message", "请选择要执行的检查项");
                return ResponseEntity.badRequest().body(response);
            }

            // 执行检查
            List<CheckResult> results = serverCheckService.executeCheckItems(request);

            // 统计结果
            long successCount = results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
            long failCount = results.size() - successCount;

            response.put("success", true);
            response.put("results", results);

            Map<String, Object> summary = new HashMap<>();
            summary.put("total", results.size());
            summary.put("success", successCount);
            summary.put("fail", failCount);
            response.put("summary", summary);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("执行检查异常: {}", e.getMessage());
            response.put("success", false);
            response.put("message", "执行检查失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }
    
    @GetMapping("/check-items")
    @ResponseBody
    public ResponseEntity<List<CheckItem>> getCheckItems() {
        List<CheckItem> checkItems = serverCheckService.getAllCheckItems();
        return ResponseEntity.ok(checkItems);
    }
    
    @GetMapping("/check-items/{category}")
    @ResponseBody
    public ResponseEntity<List<CheckItem>> getCheckItemsByCategory(@PathVariable String category) {
        List<CheckItem> checkItems = serverCheckService.getCheckItemsByCategory(category);
        return ResponseEntity.ok(checkItems);
    }

    @PostMapping("/test-database")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> testDatabase(@Valid @RequestBody DatabaseConfig dbConfig) {
        Map<String, Object> response = new HashMap<>();

        try {
            logger.info("测试数据库连接: {}:{}", dbConfig.getUrl(), dbConfig.getPort());
            boolean success = databaseService.testConnection(dbConfig);

            response.put("success", success);
            if (success) {
                response.put("message", "数据库连接成功");
            } else {
                response.put("message", "数据库连接失败: " + dbConfig.getErrorMessage());
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("测试数据库连接异常: {}", e.getMessage());
            response.put("success", false);
            response.put("message", "连接异常: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }
}
