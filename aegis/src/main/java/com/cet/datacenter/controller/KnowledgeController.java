package com.cet.datacenter.controller;

import com.cet.datacenter.entity.KnowledgeItem;
import com.cet.datacenter.service.KnowledgeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 知识库控制器
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@Controller
@RequestMapping("/knowledge")
public class KnowledgeController {
    
    private static final Logger logger = LoggerFactory.getLogger(KnowledgeController.class);
    
    @Autowired
    private KnowledgeService knowledgeService;
    
    @GetMapping
    public String knowledgePage(Model model) {
        List<KnowledgeItem> knowledgeItems = knowledgeService.getAllKnowledgeItems();
        
        // 按分类分组
        Map<String, List<KnowledgeItem>> itemsByCategory = new HashMap<>();
        for (KnowledgeItem item : knowledgeItems) {
            itemsByCategory.computeIfAbsent(item.getCategory(), k -> new java.util.ArrayList<>()).add(item);
        }
        
        model.addAttribute("title", "问题球 - 知识库");
        model.addAttribute("knowledgeItems", knowledgeItems);
        model.addAttribute("itemsByCategory", itemsByCategory);
        
        return "knowledge-ball";
    }
    
    @GetMapping("/search")
    @ResponseBody
    public ResponseEntity<List<KnowledgeItem>> searchKnowledge(@RequestParam String keyword) {
        try {
            logger.info("搜索知识库: {}", keyword);
            List<KnowledgeItem> results = knowledgeService.searchKnowledgeItems(keyword);
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            logger.error("搜索知识库异常: {}", e.getMessage());
            return ResponseEntity.ok(new ArrayList<>());
        }
    }
    
    @GetMapping("/category/{category}")
    @ResponseBody
    public ResponseEntity<List<KnowledgeItem>> getKnowledgeByCategory(@PathVariable String category) {
        try {
            List<KnowledgeItem> results = knowledgeService.getKnowledgeItemsByCategory(category);
            return ResponseEntity.ok(results);
        } catch (Exception e) {
            logger.error("获取分类知识库异常: {}", e.getMessage());
            return ResponseEntity.ok(new ArrayList<>());
        }
    }
    
    @GetMapping("/item/{id}")
    public String knowledgeDetail(@PathVariable String id, Model model) {
        try {
            KnowledgeItem item = knowledgeService.getKnowledgeItemById(id);
            if (item == null) {
                model.addAttribute("error", "知识条目不存在");
                return "error";
            }
            
            String solutionContent = knowledgeService.getSolutionContent(item.getSolutionFile());
            
            model.addAttribute("title", item.getTitle());
            model.addAttribute("item", item);
            model.addAttribute("solutionContent", solutionContent);
            
            return "solution-detail";
            
        } catch (Exception e) {
            logger.error("获取知识详情异常: {}", e.getMessage());
            model.addAttribute("error", "获取知识详情失败: " + e.getMessage());
            return "error";
        }
    }
    
    @GetMapping("/solution/{id}")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getSolution(@PathVariable String id) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            KnowledgeItem item = knowledgeService.getKnowledgeItemById(id);
            if (item == null) {
                response.put("success", false);
                response.put("message", "知识条目不存在");
                return ResponseEntity.ok(response);
            }
            
            String solutionContent = knowledgeService.getSolutionContent(item.getSolutionFile());
            
            response.put("success", true);
            response.put("item", item);
            response.put("content", solutionContent);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("获取解决方案异常: {}", e.getMessage());
            response.put("success", false);
            response.put("message", "获取解决方案失败: " + e.getMessage());
            return ResponseEntity.ok(response);
        }
    }
    
    @GetMapping("/all")
    @ResponseBody
    public ResponseEntity<List<KnowledgeItem>> getAllKnowledge() {
        try {
            List<KnowledgeItem> items = knowledgeService.getAllKnowledgeItems();
            return ResponseEntity.ok(items);
        } catch (Exception e) {
            logger.error("获取所有知识库异常: {}", e.getMessage());
            return ResponseEntity.ok(new ArrayList<>());
        }
    }
}
