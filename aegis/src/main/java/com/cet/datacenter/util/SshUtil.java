package com.cet.datacenter.util;

import com.cet.datacenter.entity.ServerInfo;
import com.jcraft.jsch.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * SSH连接工具类
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@Component
public class SshUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(SshUtil.class);
    private static final Logger sshLogger = LoggerFactory.getLogger("SSH_OPERATIONS");
    
    private static final int DEFAULT_TIMEOUT = 30000;
    
    /**
     * 执行SSH命令
     */
    public String executeCommand(ServerInfo serverInfo, String command) throws Exception {
        Session session = null;
        ChannelExec channelExec = null;
        
        try {
            session = createSession(serverInfo);
            session.connect(DEFAULT_TIMEOUT);
            
            channelExec = (ChannelExec) session.openChannel("exec");
            channelExec.setCommand(command);
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ByteArrayOutputStream errorStream = new ByteArrayOutputStream();
            
            channelExec.setOutputStream(outputStream);
            channelExec.setErrStream(errorStream);
            
            channelExec.connect(DEFAULT_TIMEOUT);
            
            // 等待命令执行完成
            while (!channelExec.isClosed()) {
                Thread.sleep(100);
            }
            
            int exitCode = channelExec.getExitStatus();
            String output = outputStream.toString("UTF-8");
            String error = errorStream.toString("UTF-8");
            
            sshLogger.info("服务器: {}, 命令: {}, 退出码: {}, 输出长度: {}", 
                    serverInfo.getHost(), command, exitCode, output.length());
            
            if (exitCode != 0 && !error.isEmpty()) {
                throw new RuntimeException("命令执行失败: " + error);
            }
            
            return output;
            
        } catch (Exception e) {
            logger.error("SSH命令执行失败: 服务器={}, 命令={}, 错误={}", 
                    serverInfo.getHost(), command, e.getMessage());
            throw e;
        } finally {
            if (channelExec != null) {
                channelExec.disconnect();
            }
            if (session != null) {
                session.disconnect();
            }
        }
    }
    
    /**
     * 测试SSH连接
     */
    public boolean testConnection(ServerInfo serverInfo) {
        Session session = null;
        try {
            session = createSession(serverInfo);
            session.connect(DEFAULT_TIMEOUT);
            
            sshLogger.info("SSH连接测试成功: 服务器={}, 地址={}:{}", 
                    serverInfo.getHost(), serverInfo.getHost(), serverInfo.getPort());
            return true;
            
        } catch (Exception e) {
            logger.error("SSH连接测试失败: 服务器={}, 地址={}:{}, 错误={}", 
                    serverInfo.getHost(), serverInfo.getHost(), serverInfo.getPort(), e.getMessage());
            return false;
        } finally {
            if (session != null) {
                session.disconnect();
            }
        }
    }
    
    /**
     * 检查文件是否存在
     */
    public boolean fileExists(ServerInfo serverInfo, String filePath) throws Exception {
        String command = "test -f " + filePath + " && echo 'EXISTS' || echo 'NOT_EXISTS'";
        String result = executeCommand(serverInfo, command);
        return result.trim().equals("EXISTS");
    }
    
    /**
     * 检查进程是否运行
     */
    public boolean processRunning(ServerInfo serverInfo, String processName) throws Exception {
        String command = "pgrep -f " + processName + " | wc -l";
        String result = executeCommand(serverInfo, command);
        try {
            int count = Integer.parseInt(result.trim());
            return count > 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 检查端口是否监听
     */
    public boolean portListening(ServerInfo serverInfo, int port) throws Exception {
        String command = "netstat -tuln | grep :" + port + " | wc -l";
        String result = executeCommand(serverInfo, command);
        try {
            int count = Integer.parseInt(result.trim());
            return count > 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 创建SSH会话
     */
    private Session createSession(ServerInfo serverInfo) throws JSchException {
        JSch jsch = new JSch();
        
        // 如果有私钥文件，添加私钥
        if (serverInfo.getPrivateKeyPath() != null && !serverInfo.getPrivateKeyPath().isEmpty()) {
            jsch.addIdentity(serverInfo.getPrivateKeyPath());
        }
        
        Session session = jsch.getSession(serverInfo.getUsername(), serverInfo.getHost(), serverInfo.getPort());
        
        // 如果有密码，设置密码
        if (serverInfo.getPassword() != null && !serverInfo.getPassword().isEmpty()) {
            session.setPassword(serverInfo.getPassword());
        }
        
        // 跳过主机密钥检查
        session.setConfig("StrictHostKeyChecking", "no");
        
        return session;
    }
}
