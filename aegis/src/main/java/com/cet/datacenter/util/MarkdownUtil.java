package com.cet.datacenter.util;

import org.commonmark.Extension;
import org.commonmark.ext.gfm.tables.TablesExtension;
import org.commonmark.node.Node;
import org.commonmark.parser.Parser;
import org.commonmark.renderer.html.HtmlRenderer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * Markdown解析工具类
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
@Component
public class MarkdownUtil {
    
    private static final Logger logger = LoggerFactory.getLogger(MarkdownUtil.class);
    
    private final Parser parser;
    private final HtmlRenderer renderer;
    
    public MarkdownUtil() {
        // 支持表格扩展
        List<Extension> extensions = Arrays.asList(TablesExtension.create());
        
        parser = Parser.builder()
                .extensions(extensions)
                .build();
        
        renderer = HtmlRenderer.builder()
                .extensions(extensions)
                .build();
    }
    
    /**
     * 将Markdown文本转换为HTML
     */
    public String markdownToHtml(String markdown) {
        try {
            if (markdown == null || markdown.trim().isEmpty()) {
                return "<p>内容为空</p>";
            }
            
            Node document = parser.parse(markdown);
            String html = renderer.render(document);
            
            // 添加一些样式类
            html = html.replaceAll("<table>", "<table class=\"table table-striped table-bordered\">");
            html = html.replaceAll("<code>", "<code class=\"code-inline\">");
            html = html.replaceAll("<pre><code", "<pre class=\"code-block\"><code");
            
            return html;
            
        } catch (Exception e) {
            logger.error("Markdown转换HTML失败: {}", e.getMessage());
            return "<p class=\"error\">内容解析失败: " + e.getMessage() + "</p>";
        }
    }
    
    /**
     * 提取Markdown文本的摘要（前200个字符）
     */
    public String extractSummary(String markdown) {
        try {
            if (markdown == null || markdown.trim().isEmpty()) {
                return "无内容";
            }
            
            // 移除Markdown标记
            String plainText = markdown
                    .replaceAll("#+\\s*", "") // 移除标题标记
                    .replaceAll("\\*\\*([^*]+)\\*\\*", "$1") // 移除粗体标记
                    .replaceAll("\\*([^*]+)\\*", "$1") // 移除斜体标记
                    .replaceAll("`([^`]+)`", "$1") // 移除代码标记
                    .replaceAll("\\[([^\\]]+)\\]\\([^)]+\\)", "$1") // 移除链接标记
                    .replaceAll("\\n+", " ") // 替换换行为空格
                    .trim();
            
            if (plainText.length() <= 200) {
                return plainText;
            }
            
            return plainText.substring(0, 200) + "...";
            
        } catch (Exception e) {
            logger.error("提取Markdown摘要失败: {}", e.getMessage());
            return "摘要提取失败";
        }
    }
    
    /**
     * 验证Markdown语法
     */
    public boolean isValidMarkdown(String markdown) {
        try {
            if (markdown == null) {
                return false;
            }
            
            parser.parse(markdown);
            return true;
            
        } catch (Exception e) {
            logger.warn("Markdown语法验证失败: {}", e.getMessage());
            return false;
        }
    }
}
