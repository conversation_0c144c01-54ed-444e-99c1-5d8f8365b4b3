package com.cet.datacenter.entity;

/**
 * 数据库配置实体类
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public class DatabaseConfig {
    
    private String url;
    private String port;
    private String username;
    private String password;
    private String database;
    private String projectName;
    private boolean connected;
    private String errorMessage;
    
    // 构造函数
    public DatabaseConfig() {
    }
    
    public DatabaseConfig(String url, String port, String username, String password) {
        this.url = url;
        this.port = port;
        this.username = username;
        this.password = password;
    }
    
    // Getter和Setter方法
    public String getUrl() {
        return url;
    }
    
    public void setUrl(String url) {
        this.url = url;
    }
    
    public String getPort() {
        return port;
    }
    
    public void setPort(String port) {
        this.port = port;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getDatabase() {
        return database;
    }
    
    public void setDatabase(String database) {
        this.database = database;
    }

    public String getProjectName() {
        return projectName;
    }

    public void setProjectName(String projectName) {
        this.projectName = projectName;
    }

    public boolean isConnected() {
        return connected;
    }
    
    public void setConnected(boolean connected) {
        this.connected = connected;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    /**
     * 获取完整的JDBC URL
     */
    public String getJdbcUrl() {
        if (url != null && port != null) {
            String dbUrl;
            if (url.startsWith("jdbc:postgresql://")) {
                dbUrl = url;
            } else {
                dbUrl = "jdbc:postgresql://" + url + ":" + port;
                if (database != null && !database.isEmpty()) {
                    dbUrl += "/" + database;
                } else {
                    dbUrl += "/postgres"; // 默认数据库
                }
            }
            return dbUrl;
        }
        return null;
    }
    
    @Override
    public String toString() {
        return "DatabaseConfig{" +
                "url='" + url + '\'' +
                ", port='" + port + '\'' +
                ", username='" + username + '\'' +
                ", database='" + database + '\'' +
                ", connected=" + connected +
                '}';
    }
}
