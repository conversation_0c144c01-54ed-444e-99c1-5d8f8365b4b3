package com.cet.datacenter.entity;

import java.util.List;

/**
 * 知识库条目实体类
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
public class KnowledgeItem {
    
    private String id;
    private String title;
    private String description;
    private String category;
    private List<String> keywords;
    private String solutionFile; // Markdown文件路径
    private String color; // 问题球颜色
    private int priority = 0; // 优先级，数字越大越重要
    private boolean enabled = true;
    
    // 构造函数
    public KnowledgeItem() {}
    
    public KnowledgeItem(String id, String title, String description, String category) {
        this.id = id;
        this.title = title;
        this.description = description;
        this.category = category;
    }
    
    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public List<String> getKeywords() {
        return keywords;
    }
    
    public void setKeywords(List<String> keywords) {
        this.keywords = keywords;
    }
    
    public String getSolutionFile() {
        return solutionFile;
    }
    
    public void setSolutionFile(String solutionFile) {
        this.solutionFile = solutionFile;
    }
    
    public String getColor() {
        return color;
    }
    
    public void setColor(String color) {
        this.color = color;
    }
    
    public int getPriority() {
        return priority;
    }
    
    public void setPriority(int priority) {
        this.priority = priority;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    @Override
    public String toString() {
        return "KnowledgeItem{" +
                "id='" + id + '\'' +
                ", title='" + title + '\'' +
                ", description='" + description + '\'' +
                ", category='" + category + '\'' +
                ", keywords=" + keywords +
                ", solutionFile='" + solutionFile + '\'' +
                ", color='" + color + '\'' +
                ", priority=" + priority +
                ", enabled=" + enabled +
                '}';
    }
}
