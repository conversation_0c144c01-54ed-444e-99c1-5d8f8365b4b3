package com.cet.datacenter.entity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 服务器信息实体类
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
public class ServerInfo {
    
    @NotBlank(message = "服务器地址不能为空")
    private String host;
    
    @NotNull(message = "端口号不能为空")
    private Integer port = 22;
    
    @NotBlank(message = "用户名不能为空")
    private String username;
    
    private String password;
    
    private String privateKeyPath;
    
    private String description;
    
    // 构造函数
    public ServerInfo() {}
    
    public ServerInfo(String name, String host, Integer port, String username) {
        this.host = host;
        this.port = port;
        this.username = username;
    }
    

    public String getHost() {
        return host;
    }
    
    public void setHost(String host) {
        this.host = host;
    }
    
    public Integer getPort() {
        return port;
    }
    
    public void setPort(Integer port) {
        this.port = port;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getPrivateKeyPath() {
        return privateKeyPath;
    }
    
    public void setPrivateKeyPath(String privateKeyPath) {
        this.privateKeyPath = privateKeyPath;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    @Override
    public String toString() {
        return "ServerInfo{" +
                ", host='" + host + '\'' +
                ", port=" + port +
                ", username='" + username + '\'' +
                ", description='" + description + '\'' +
                '}';
    }
}
