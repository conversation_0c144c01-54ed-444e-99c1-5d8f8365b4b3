package com.cet.datacenter.entity;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 检查结果实体类
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
public class CheckResult {
    
    private String checkItemId;
    private String checkItemName;
    private String serverName;
    private boolean success;
    private String actualResult;
    private String expectedResult;
    private String errorMessage;
    private LocalDateTime checkTime;
    private long executionTime; // 执行时间（毫秒）
    private List<CheckStep> checkSteps; // 检查步骤列表
    
    // 构造函数
    public CheckResult() {
        this.checkTime = LocalDateTime.now();
        this.checkSteps = new ArrayList<>();
    }
    
    public CheckResult(String checkItemId, String checkItemName, String serverName) {
        this();
        this.checkItemId = checkItemId;
        this.checkItemName = checkItemName;
        this.serverName = serverName;
    }
    
    // Getter和Setter方法
    public String getCheckItemId() {
        return checkItemId;
    }
    
    public void setCheckItemId(String checkItemId) {
        this.checkItemId = checkItemId;
    }
    
    public String getCheckItemName() {
        return checkItemName;
    }
    
    public void setCheckItemName(String checkItemName) {
        this.checkItemName = checkItemName;
    }
    
    public String getServerName() {
        return serverName;
    }
    
    public void setServerName(String serverName) {
        this.serverName = serverName;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
    }
    
    public String getActualResult() {
        return actualResult;
    }
    
    public void setActualResult(String actualResult) {
        this.actualResult = actualResult;
    }
    
    public String getExpectedResult() {
        return expectedResult;
    }
    
    public void setExpectedResult(String expectedResult) {
        this.expectedResult = expectedResult;
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public LocalDateTime getCheckTime() {
        return checkTime;
    }
    
    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }
    
    public long getExecutionTime() {
        return executionTime;
    }
    
    public void setExecutionTime(long executionTime) {
        this.executionTime = executionTime;
    }

    public List<CheckStep> getCheckSteps() {
        return checkSteps;
    }

    public void setCheckSteps(List<CheckStep> checkSteps) {
        this.checkSteps = checkSteps;
    }

    /**
     * 添加检查步骤
     */
    public void addCheckStep(CheckStep step) {
        if (this.checkSteps == null) {
            this.checkSteps = new ArrayList<>();
        }
        this.checkSteps.add(step);
    }
    
    /**
     * 获取状态描述
     */
    public String getStatusText() {
        return success ? "成功" : "失败";
    }
    
    /**
     * 获取状态CSS类
     */
    public String getStatusClass() {
        return success ? "success" : "error";
    }
    
    @Override
    public String toString() {
        return "CheckResult{" +
                "checkItemId='" + checkItemId + '\'' +
                ", checkItemName='" + checkItemName + '\'' +
                ", serverName='" + serverName + '\'' +
                ", success=" + success +
                ", actualResult='" + actualResult + '\'' +
                ", errorMessage='" + errorMessage + '\'' +
                ", checkTime=" + checkTime +
                ", executionTime=" + executionTime +
                '}';
    }
}
