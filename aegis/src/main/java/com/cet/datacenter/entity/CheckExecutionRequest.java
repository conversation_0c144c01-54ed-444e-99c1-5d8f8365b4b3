package com.cet.datacenter.entity;

import java.util.List;

/**
 * 检查执行请求实体类
 * 包含所有服务器配置和数据库配置
 * 
 * <AUTHOR>
 * @date 2025-08-22
 */
public class CheckExecutionRequest {
    
    private ServerInfo vipServer;           // VIP服务器配置
    private ServerInfo mainServer;          // 业务主服务器配置
    private ServerInfo backupServer;        // 业务备服务器配置
    private ServerInfo collectMainServer;   // 采集主服务器配置
    private ServerInfo collectBackupServer; // 采集备服务器配置
    private DatabaseConfig databaseConfig;  // 数据库配置
    private List<String> checkItems;        // 要执行的检查项ID列表
    private String alarmPointCount;         // 告警点总数（用于告警点超限检查）
    private String meterType;               // 表计类型（用于同类设备测点检查）
    private String subMeterType;            // 表计型号（用于同类设备测点检查）
    
    // 构造函数
    public CheckExecutionRequest() {
    }
    
    // Getter和Setter方法
    public ServerInfo getVipServer() {
        return vipServer;
    }
    
    public void setVipServer(ServerInfo vipServer) {
        this.vipServer = vipServer;
    }
    
    public ServerInfo getMainServer() {
        return mainServer;
    }
    
    public void setMainServer(ServerInfo mainServer) {
        this.mainServer = mainServer;
    }
    
    public ServerInfo getBackupServer() {
        return backupServer;
    }
    
    public void setBackupServer(ServerInfo backupServer) {
        this.backupServer = backupServer;
    }
    
    public ServerInfo getCollectMainServer() {
        return collectMainServer;
    }
    
    public void setCollectMainServer(ServerInfo collectMainServer) {
        this.collectMainServer = collectMainServer;
    }
    
    public ServerInfo getCollectBackupServer() {
        return collectBackupServer;
    }
    
    public void setCollectBackupServer(ServerInfo collectBackupServer) {
        this.collectBackupServer = collectBackupServer;
    }
    
    public DatabaseConfig getDatabaseConfig() {
        return databaseConfig;
    }
    
    public void setDatabaseConfig(DatabaseConfig databaseConfig) {
        this.databaseConfig = databaseConfig;
    }
    
    public List<String> getCheckItems() {
        return checkItems;
    }
    
    public void setCheckItems(List<String> checkItems) {
        this.checkItems = checkItems;
    }

    public String getAlarmPointCount() {
        return alarmPointCount;
    }

    public void setAlarmPointCount(String alarmPointCount) {
        this.alarmPointCount = alarmPointCount;
    }

    public String getMeterType() {
        return meterType;
    }

    public void setMeterType(String meterType) {
        this.meterType = meterType;
    }

    public String getSubMeterType() {
        return subMeterType;
    }

    public void setSubMeterType(String subMeterType) {
        this.subMeterType = subMeterType;
    }

    /**
     * 根据类型获取服务器配置
     */
    public ServerInfo getServerByType(String serverType) {
        switch (serverType) {
            case "VIP":
                return vipServer;
            case "MAIN":
                return mainServer;
            case "BACKUP":
                return backupServer;
            case "COLLECT_MAIN":
                return collectMainServer;
            case "COLLECT_BACKUP":
                return collectBackupServer;
            default:
                return vipServer; // 默认返回VIP服务器
        }
    }
    
    @Override
    public String toString() {
        return "CheckExecutionRequest{" +
                "vipServer=" + (vipServer != null ? vipServer.getHost() : "null") +
                ", mainServer=" + (mainServer != null ? mainServer.getHost() : "null") +
                ", backupServer=" + (backupServer != null ? backupServer.getHost() : "null") +
                ", collectMainServer=" + (collectMainServer != null ? collectMainServer.getHost() : "null") +
                ", collectBackupServer=" + (collectBackupServer != null ? collectBackupServer.getHost() : "null") +
                ", databaseConfig=" + (databaseConfig != null ? databaseConfig.getUrl() : "null") +
                ", checkItems=" + (checkItems != null ? checkItems.size() : 0) + " items" +
                '}';
    }
}
