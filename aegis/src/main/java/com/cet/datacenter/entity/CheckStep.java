package com.cet.datacenter.entity;

/**
 * 检查步骤实体类
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
public class CheckStep {
    
    private String stepName;        // 步骤名称
    private String stepDescription; // 步骤描述
    private String stepType;        // 步骤类型：SSH_CONNECT, SSH_COMMAND, DB_CONNECT, DB_QUERY, ANALYSIS
    private String command;         // 执行的命令或SQL
    private String result;          // 步骤执行结果
    private boolean success;        // 步骤是否成功
    private String errorMessage;    // 错误信息
    private long executionTime;     // 执行时间（毫秒）
    private String status;          // 步骤状态：PENDING, RUNNING, SUCCESS, ERROR
    
    // 构造函数
    public CheckStep() {
        this.status = "PENDING";
    }
    
    public CheckStep(String stepName, String stepDescription, String stepType) {
        this();
        this.stepName = stepName;
        this.stepDescription = stepDescription;
        this.stepType = stepType;
    }
    
    // Getter和Setter方法
    public String getStepName() {
        return stepName;
    }
    
    public void setStepName(String stepName) {
        this.stepName = stepName;
    }
    
    public String getStepDescription() {
        return stepDescription;
    }
    
    public void setStepDescription(String stepDescription) {
        this.stepDescription = stepDescription;
    }
    
    public String getStepType() {
        return stepType;
    }
    
    public void setStepType(String stepType) {
        this.stepType = stepType;
    }
    
    public String getCommand() {
        return command;
    }
    
    public void setCommand(String command) {
        this.command = command;
    }
    
    public String getResult() {
        return result;
    }
    
    public void setResult(String result) {
        this.result = result;
    }
    
    public boolean isSuccess() {
        return success;
    }
    
    public void setSuccess(boolean success) {
        this.success = success;
        this.status = success ? "SUCCESS" : "ERROR";
    }
    
    public String getErrorMessage() {
        return errorMessage;
    }
    
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
    
    public long getExecutionTime() {
        return executionTime;
    }
    
    public void setExecutionTime(long executionTime) {
        this.executionTime = executionTime;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    /**
     * 获取步骤图标
     */
    public String getStepIcon() {
        switch (stepType) {
            case "SSH_CONNECT":
                return "fas fa-plug";
            case "SSH_COMMAND":
                return "fas fa-terminal";
            case "DB_CONNECT":
                return "fas fa-database";
            case "DB_QUERY":
                return "fas fa-search";
            case "ANALYSIS":
                return "fas fa-check-circle";
            default:
                return "fas fa-cog";
        }
    }
    
    /**
     * 获取状态CSS类
     */
    public String getStatusClass() {
        switch (status) {
            case "RUNNING":
                return "active";
            case "SUCCESS":
                return "success";
            case "ERROR":
                return "error";
            default:
                return "";
        }
    }
    
    @Override
    public String toString() {
        return "CheckStep{" +
                "stepName='" + stepName + '\'' +
                ", stepType='" + stepType + '\'' +
                ", success=" + success +
                ", status='" + status + '\'' +
                ", executionTime=" + executionTime +
                '}';
    }
}
