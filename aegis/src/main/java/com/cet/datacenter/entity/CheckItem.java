package com.cet.datacenter.entity;

/**
 * 检查项实体类
 * 
 * <AUTHOR>
 * @date 2025-08-15
 */
public class CheckItem {
    
    private String id;
    private String name;
    private String description;
    private String category;
    private String command;
    private String expectedResult;
    private String checkType; // COMMAND, FILE_EXISTS, PROCESS_RUNNING, PORT_LISTENING
    private boolean enabled = true;
    
    // 构造函数
    public CheckItem() {}
    
    public CheckItem(String id, String name, String description, String category, String command, String checkType) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.category = category;
        this.command = command;
        this.checkType = checkType;
    }
    
    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public String getCommand() {
        return command;
    }
    
    public void setCommand(String command) {
        this.command = command;
    }
    
    public String getExpectedResult() {
        return expectedResult;
    }
    
    public void setExpectedResult(String expectedResult) {
        this.expectedResult = expectedResult;
    }
    
    public String getCheckType() {
        return checkType;
    }
    
    public void setCheckType(String checkType) {
        this.checkType = checkType;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    @Override
    public String toString() {
        return "CheckItem{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", category='" + category + '\'' +
                ", command='" + command + '\'' +
                ", checkType='" + checkType + '\'' +
                ", enabled=" + enabled +
                '}';
    }
}
